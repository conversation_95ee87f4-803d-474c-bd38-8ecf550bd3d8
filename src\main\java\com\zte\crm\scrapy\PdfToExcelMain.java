package com.zte.crm.scrapy;

import com.zte.crm.scrapy.service.IPdfToExcelService;
import com.zte.crm.scrapy.service.impl.PdfToExcelServiceImpl;

import java.io.File;
import java.util.Scanner;

/**
 * PDF转Excel命令行工具
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-13
 */
public class PdfToExcelMain {
    
    private static final IPdfToExcelService pdfToExcelService = new PdfToExcelServiceImpl();
    
    public static void main(String[] args) {
        System.out.println("=== PDF转Excel工具 ===");
        System.out.println("支持表格格式保持和对齐");
        System.out.println();
        
        if (args.length >= 2) {
            // 命令行参数模式
            String pdfPath = args[0];
            String excelPath = args[1];
            convertFile(pdfPath, excelPath);
        } else if (args.length == 1 && "test".equals(args[0])) {
            // 测试模式
            runTest();
        } else {
            // 交互模式
            runInteractiveMode();
        }
    }
    
    /**
     * 交互模式
     */
    private static void runInteractiveMode() {
        Scanner scanner = new Scanner(System.in);
        
        while (true) {
            System.out.println("\n请选择操作:");
            System.out.println("1. 转换PDF文件");
            System.out.println("2. 测试参照文件");
            System.out.println("3. 退出");
            System.out.print("请输入选项 (1-3): ");
            
            String choice = scanner.nextLine().trim();
            
            switch (choice) {
                case "1":
                    convertInteractive(scanner);
                    break;
                case "2":
                    runTest();
                    break;
                case "3":
                    System.out.println("再见!");
                    return;
                default:
                    System.out.println("无效选项，请重新输入");
            }
        }
    }
    
    /**
     * 交互式转换
     */
    private static void convertInteractive(Scanner scanner) {
        System.out.print("请输入PDF文件路径: ");
        String pdfPath = scanner.nextLine().trim();
        
        if (pdfPath.isEmpty()) {
            System.out.println("PDF文件路径不能为空");
            return;
        }
        
        System.out.print("请输入Excel输出路径 (留空则自动生成): ");
        String excelPath = scanner.nextLine().trim();
        
        if (excelPath.isEmpty()) {
            // 自动生成Excel文件名
            if (pdfPath.toLowerCase().endsWith(".pdf")) {
                excelPath = pdfPath.substring(0, pdfPath.length() - 4) + ".xlsx";
            } else {
                excelPath = pdfPath + ".xlsx";
            }
            System.out.println("自动生成Excel文件路径: " + excelPath);
        }
        
        convertFile(pdfPath, excelPath);
    }
    
    /**
     * 转换文件
     */
    private static void convertFile(String pdfPath, String excelPath) {
        try {
            System.out.println("\n开始转换...");
            System.out.println("PDF文件: " + pdfPath);
            System.out.println("Excel文件: " + excelPath);
            
            // 验证PDF文件
            File pdfFile = new File(pdfPath);
            if (!pdfFile.exists()) {
                System.err.println("错误: PDF文件不存在 - " + pdfPath);
                return;
            }
            
            if (!pdfToExcelService.validatePdfFile(pdfFile)) {
                System.err.println("错误: 无效的PDF文件 - " + pdfPath);
                return;
            }
            
            // 显示PDF信息
            int pageCount = pdfToExcelService.getPdfPageCount(pdfFile);
            long fileSize = pdfFile.length();
            System.out.println("PDF信息: " + pageCount + "页, " + String.format("%.2f KB", fileSize / 1024.0));
            
            // 执行转换
            long startTime = System.currentTimeMillis();
            String result = pdfToExcelService.convertPdfToExcel(pdfPath, excelPath);
            long endTime = System.currentTimeMillis();
            
            System.out.println("\n" + result);
            System.out.println("总耗时: " + (endTime - startTime) + " ms");
            
        } catch (Exception e) {
            System.err.println("转换失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 运行测试
     */
    private static void runTest() {
        System.out.println("\n=== 测试参照文件 ===");
        
        String testPdfPath = "doc/Test_HK4934_page10.pdf";
        String testExcelPath = "doc/Test_HK4934_page10_converted.xlsx";
        String referencePath = "doc/Test_HK4934_page10.xlsx";
        
        File testPdf = new File(testPdfPath);
        File referenceExcel = new File(referencePath);
        
        if (!testPdf.exists()) {
            System.out.println("测试PDF文件不存在: " + testPdfPath);
            return;
        }
        
        if (!referenceExcel.exists()) {
            System.out.println("参照Excel文件不存在: " + referencePath);
        } else {
            System.out.println("参照Excel文件: " + referencePath + 
                " (大小: " + String.format("%.2f KB", referenceExcel.length() / 1024.0) + ")");
        }
        
        convertFile(testPdfPath, testExcelPath);
        
        // 比较结果
        File convertedExcel = new File(testExcelPath);
        if (convertedExcel.exists()) {
            System.out.println("\n=== 转换结果比较 ===");
            if (referenceExcel.exists()) {
                System.out.println("参照文件大小: " + String.format("%.2f KB", referenceExcel.length() / 1024.0));
            }
            System.out.println("转换文件大小: " + String.format("%.2f KB", convertedExcel.length() / 1024.0));
            System.out.println("转换文件路径: " + convertedExcel.getAbsolutePath());
            
            System.out.println("\n请手动比较两个Excel文件的内容和格式是否一致。");
        }
    }
}
