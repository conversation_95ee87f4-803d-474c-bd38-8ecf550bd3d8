package com.zte.crm.scrapy.controller;

import com.zte.crm.scrapy.service.IPdfToExcelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * PDF转Excel控制器
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-13
 */
@Slf4j
@RestController
@RequestMapping("/api/pdf-excel")
@Api(tags = "PDF转Excel服务")
public class PdfToExcelController {
    
    @Autowired
    private IPdfToExcelService pdfToExcelService;
    
    /**
     * 上传PDF文件并转换为Excel
     */
    @PostMapping("/convert")
    @ApiOperation("上传PDF文件并转换为Excel")
    public ResponseEntity<Map<String, Object>> convertPdfToExcel(
            @ApiParam("PDF文件") @RequestParam("file") MultipartFile file) {
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 验证文件
            if (file.isEmpty()) {
                result.put("success", false);
                result.put("message", "请选择PDF文件");
                return ResponseEntity.badRequest().body(result);
            }
            
            if (!file.getOriginalFilename().toLowerCase().endsWith(".pdf")) {
                result.put("success", false);
                result.put("message", "请上传PDF格式的文件");
                return ResponseEntity.badRequest().body(result);
            }
            
            // 生成文件名
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            String originalName = file.getOriginalFilename();
            String baseName = originalName.substring(0, originalName.lastIndexOf('.'));
            String excelFileName = baseName + "_" + timestamp + ".xlsx";
            
            // 创建临时目录
            Path tempDir = Paths.get(System.getProperty("java.io.tmpdir"), "pdf-excel-converter");
            Files.createDirectories(tempDir);
            
            // 保存上传的PDF文件
            Path pdfPath = tempDir.resolve(file.getOriginalFilename());
            file.transferTo(pdfPath.toFile());
            
            // 生成Excel文件路径
            Path excelPath = tempDir.resolve(excelFileName);
            
            // 执行转换
            String convertResult = pdfToExcelService.convertPdfToExcel(
                pdfPath.toFile(), excelPath.toFile());
            
            result.put("success", true);
            result.put("message", "转换成功");
            result.put("details", convertResult);
            result.put("downloadUrl", "/api/pdf-excel/download/" + excelFileName);
            result.put("excelFileName", excelFileName);
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("PDF转Excel失败", e);
            result.put("success", false);
            result.put("message", "转换失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }
    
    /**
     * 下载转换后的Excel文件
     */
    @GetMapping("/download/{fileName}")
    @ApiOperation("下载转换后的Excel文件")
    public ResponseEntity<Resource> downloadExcel(
            @ApiParam("文件名") @PathVariable String fileName) {
        
        try {
            Path tempDir = Paths.get(System.getProperty("java.io.tmpdir"), "pdf-excel-converter");
            Path filePath = tempDir.resolve(fileName);
            
            if (!Files.exists(filePath)) {
                return ResponseEntity.notFound().build();
            }
            
            Resource resource = new FileSystemResource(filePath.toFile());
            
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + fileName + "\"")
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .body(resource);
                    
        } catch (Exception e) {
            log.error("下载文件失败: {}", fileName, e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * 转换本地PDF文件
     */
    @PostMapping("/convert-local")
    @ApiOperation("转换本地PDF文件")
    public ResponseEntity<Map<String, Object>> convertLocalPdf(
            @ApiParam("PDF文件路径") @RequestParam String pdfPath,
            @ApiParam("Excel输出路径") @RequestParam String excelPath) {
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            String convertResult = pdfToExcelService.convertPdfToExcel(pdfPath, excelPath);
            
            result.put("success", true);
            result.put("message", "转换成功");
            result.put("details", convertResult);
            result.put("excelPath", excelPath);
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("PDF转Excel失败", e);
            result.put("success", false);
            result.put("message", "转换失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }
    
    /**
     * 获取PDF文件信息
     */
    @PostMapping("/info")
    @ApiOperation("获取PDF文件信息")
    public ResponseEntity<Map<String, Object>> getPdfInfo(
            @ApiParam("PDF文件") @RequestParam("file") MultipartFile file) {
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            if (file.isEmpty()) {
                result.put("success", false);
                result.put("message", "请选择PDF文件");
                return ResponseEntity.badRequest().body(result);
            }
            
            // 保存临时文件
            Path tempDir = Paths.get(System.getProperty("java.io.tmpdir"), "pdf-excel-converter");
            Files.createDirectories(tempDir);
            Path tempFile = tempDir.resolve("temp_" + file.getOriginalFilename());
            file.transferTo(tempFile.toFile());
            
            // 获取PDF信息
            boolean isValid = pdfToExcelService.validatePdfFile(tempFile.toFile());
            int pageCount = isValid ? pdfToExcelService.getPdfPageCount(tempFile.toFile()) : 0;
            
            result.put("success", true);
            result.put("fileName", file.getOriginalFilename());
            result.put("fileSize", file.getSize());
            result.put("isValid", isValid);
            result.put("pageCount", pageCount);
            
            // 清理临时文件
            Files.deleteIfExists(tempFile);
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("获取PDF信息失败", e);
            result.put("success", false);
            result.put("message", "获取信息失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }
}
