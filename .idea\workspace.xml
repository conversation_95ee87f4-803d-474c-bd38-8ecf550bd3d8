<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="0a02cf87-8258-439a-8dde-3312a5e5204f" name="Changes" comment="1、极致精简" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="CompilerWorkspaceConfiguration">
    <option name="MAKE_PROJECT_ON_SAVE" value="true" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Interface" />
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="master" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitToolBoxStore">
    <option name="recentBranches">
      <RecentBranches>
        <option name="branchesForRepo">
          <list>
            <RecentBranchesForRepo>
              <option name="branches">
                <list>
                  <RecentBranch>
                    <option name="branchName" value="fast" />
                    <option name="lastUsedInstant" value="1755070790" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="master" />
                    <option name="lastUsedInstant" value="1755070789" />
                  </RecentBranch>
                </list>
              </option>
              <option name="repositoryRootUrl" value="file://$PROJECT_DIR$" />
            </RecentBranchesForRepo>
          </list>
        </option>
      </RecentBranches>
    </option>
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="jar://$PROJECT_DIR$/../../../Install/.m2/com/baomidou/mybatis-plus-core/3.5.1/mybatis-plus-core-3.5.1-sources.jar!/com/baomidou/mybatisplus/core/mapper/BaseMapper.java" root0="SKIP_INSPECTION" />
  </component>
  <component name="JRebelWorkspace">
    <option name="jrebelEnabledAutocompile" value="true" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="localRepository" value="D:\JAVA\Install\.m2" />
        <option name="useMavenConfig" value="false" />
        <option name="userSettingsFile" value="D:\JAVA\Install\maven\apache-maven-3.6.2\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="2bOGJdJxMZRs46QzXygBfgZZ7Bd" />
  <component name="ProjectLevelVcsManager" settingsEditedManually="true" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Application.AESEncryptionUtil.executor": "Run",
    "Application.AESEncryptionUtilNew.executor": "Run",
    "Application.AESUtil.executor": "Run",
    "Application.ExcelMerger.executor": "Run",
    "Application.PageParseProcessor.executor": "Run",
    "Application.S3UploadBigFileServiceTest.executor": "Run",
    "Application.S3UploadService.executor": "Run",
    "Application.S3UploadServiceTest.executor": "Run",
    "Application.SM3Utils.executor": "Run",
    "Application.ScrapyNewsTitleCommonServiceImpl.executor": "Run",
    "Application.SpiderApplication.executor": "Debug",
    "Application.com.zte.crm.aws.service.impl.S3UploadService.executor": "Run",
    "Application.com.zte.crm.scrapy.web.common.downloader.PageParseProcessor.executor": "Run",
    "Maven.notice-spider [install].executor": "Run",
    "Maven.notice-spider [org.apache.maven.plugins:maven-deploy-plugin:2.8.2:deploy-file].executor": "Run",
    "Maven.notice-spider [org.apache.maven.plugins:maven-install-plugin:2.5.2:install].executor": "Run",
    "Maven.notice-spider [org.apache.maven.plugins:maven-jar-plugin:3.2.0:jar].executor": "Run",
    "Maven.notice-spider [package].executor": "Run",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "Spring Boot.SpiderApplication.executor": "Run",
    "WebServerToolWindowFactoryState": "false",
    "com.codeium.enabled": "true",
    "git-widget-placeholder": "fast",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "D:/JAVA/workspace/AI/alix-spider-master/doc",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "Libraries",
    "project.structure.proportion": "0.15",
    "project.structure.side.proportion": "0.608046",
    "settings.editor.selected.configurable": "MavenSettings",
    "spring.configuration.checksum": "b295e8cfd6ce5523fa6361952cbb58b0",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RebelAgentSelection">
    <selection>jr</selection>
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\JAVA\workspace\AI\alix-spider-master\doc" />
      <recent name="D:\JAVA\workspace\AI\alix-spider-master\lib" />
      <recent name="D:\JAVA\workspace\AI\alix-spider-master\src\main\java\ai\icrmrobot\common\model" />
      <recent name="D:\JAVA\workspace\AI\alix-spider-master\src\main\java\ai" />
      <recent name="D:\JAVA\workspace\AI\alix-spider-master\src\main\resources\mapper\dao" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\JAVA\workspace\AI\alix-spider-master\src\main\java" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.zte.crm.aws.service.impl" />
      <recent name="com.zte.crm.aws" />
      <recent name="com.zte.crm.scrapy.ai.entity" />
      <recent name="com.zte.crm.scrapy.ai.controller" />
      <recent name="com.zte.crm.scrapy.ai.service.impl" />
    </key>
  </component>
  <component name="RunManager" selected="Spring Boot.SpiderApplication">
    <configuration name="AESEncryptionUtil" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="ai.AESEncryptionUtil" />
      <module name="notice-spider" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="ai.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ExcelMerger" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.zte.crm.scrapy.ExcelMerger" />
      <module name="notice-spider" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.zte.crm.scrapy.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="S3UploadBigFileServiceTest" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.zte.crm.aws.service.impl.S3UploadBigFileServiceTest" />
      <module name="notice-spider" />
      <shortenClasspath name="MANIFEST" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.zte.crm.aws.service.impl.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="S3UploadService" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="your.S3UploadService" />
      <module name="notice-spider" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="your.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="S3UploadServiceTest" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.zte.crm.aws.service.impl.S3UploadServiceTest" />
      <module name="notice-spider" />
      <shortenClasspath name="MANIFEST" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.zte.crm.aws.service.impl.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SpiderApplication" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.zte.crm.scrapy.SpiderApplication" />
      <module name="notice-spider" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.zte.crm.scrapy.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="Application" factoryName="Application">
      <shortenClasspath name="MANIFEST" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SpiderApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="notice-spider" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.zte.crm.scrapy.SpiderApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Application.S3UploadServiceTest" />
        <item itemvalue="Application.S3UploadBigFileServiceTest" />
        <item itemvalue="Application.S3UploadService" />
        <item itemvalue="Application.ExcelMerger" />
        <item itemvalue="Application.SpiderApplication" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9f38398b9061-39b83d9b5494-intellij.indexing.shared.core-IU-241.19416.15" />
        <option value="bundled-js-predefined-1d06a55b98c1-0b3e54e931b4-JavaScript-IU-241.19416.15" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="0a02cf87-8258-439a-8dde-3312a5e5204f" name="Changes" comment="" />
      <created>1706078641848</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1706078641848</updated>
      <workItem from="1708674213956" duration="1316000" />
      <workItem from="1709881148890" duration="175000" />
      <workItem from="1709881343312" duration="1116000" />
      <workItem from="1710061916466" duration="50000" />
      <workItem from="1710296729854" duration="10478000" />
      <workItem from="1710319081830" duration="154000" />
      <workItem from="1710319302753" duration="135000" />
      <workItem from="1733279997058" duration="1833000" />
      <workItem from="1735869815336" duration="3159000" />
      <workItem from="1735895222482" duration="731000" />
      <workItem from="1740127158972" duration="2135000" />
      <workItem from="1740192361350" duration="3387000" />
      <workItem from="1740533729357" duration="2290000" />
      <workItem from="1740538991792" duration="1571000" />
      <workItem from="1740553079765" duration="8255000" />
      <workItem from="1742181098284" duration="918000" />
      <workItem from="1742456788926" duration="200000" />
      <workItem from="1742457008920" duration="690000" />
      <workItem from="1743573650431" duration="4291000" />
      <workItem from="1743581017198" duration="1344000" />
      <workItem from="1745490273611" duration="631000" />
      <workItem from="1747706234020" duration="4652000" />
      <workItem from="1752905742617" duration="564000" />
      <workItem from="1754989322012" duration="4250000" />
    </task>
    <task id="LOCAL-00001" summary="1、init git">
      <option name="closed" value="true" />
      <created>1708500968153</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1708500968153</updated>
    </task>
    <task id="LOCAL-00002" summary="1、init git 2">
      <option name="closed" value="true" />
      <created>1708675277791</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1708675277791</updated>
    </task>
    <task id="LOCAL-00003" summary="1、add aws">
      <option name="closed" value="true" />
      <created>1752905992276</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1752905992277</updated>
    </task>
    <task id="LOCAL-00004" summary="1、add aws">
      <option name="closed" value="true" />
      <created>1752906021604</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1752906021604</updated>
    </task>
    <task id="LOCAL-00005" summary="1、极致精简">
      <option name="closed" value="true" />
      <created>1755073675889</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1755073675889</updated>
    </task>
    <option name="localTasksCounter" value="6" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.History.Properties">
    <option name="COLUMN_ID_ORDER">
      <list>
        <option value="Default.Root" />
        <option value="Default.Author" />
        <option value="Default.Date" />
        <option value="Default.Subject" />
        <option value="Space.CommitStatus" />
      </list>
    </option>
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CHECK_CODE_SMELLS_BEFORE_PROJECT_COMMIT" value="false" />
    <option name="CHECK_NEW_TODO" value="false" />
    <MESSAGE value="1、init git" />
    <MESSAGE value="1、init git 2" />
    <MESSAGE value="1、add aws" />
    <MESSAGE value="1、极致精简" />
    <option name="LAST_COMMIT_MESSAGE" value="1、极致精简" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/zte/crm/scrapy/auto/service/impl/ScrapyNewsTitleCommonServiceImpl.java</url>
          <line>142</line>
          <option name="timeStamp" value="3" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/zte/crm/scrapy/auto/service/impl/ScrapyNewsTitleCommonServiceImpl.java</url>
          <line>173</line>
          <option name="timeStamp" value="4" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/zte/crm/scrapy/auto/service/impl/ScrapyNewsTitleCommonServiceImpl.java</url>
          <line>213</line>
          <option name="timeStamp" value="5" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/zte/crm/scrapy/web/mobile/ChinaMobilSpider.java</url>
          <line>54</line>
          <option name="timeStamp" value="6" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/zte/crm/scrapy/web/common/constant/ScrapyWebEnum.java</url>
          <line>34</line>
          <option name="timeStamp" value="7" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>