package com.zte.crm.scrapy;

import com.zte.crm.scrapy.util.PdfToExcelConverter;

import java.io.File;

/**
 * 简单的PDF转Excel测试类
 * 不依赖Spring Boot，可以直接运行
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-13
 */
public class SimplePdfToExcelTest {
    
    public static void main(String[] args) {
        System.out.println("=== 简单PDF转Excel测试 ===");
        
        if (args.length >= 2) {
            // 命令行参数模式
            String pdfPath = args[0];
            String excelPath = args[1];
            testConversion(pdfPath, excelPath);
        } else {
            // 默认测试参照文件
            testReferenceFile();
        }
    }
    
    /**
     * 测试参照文件转换
     */
    private static void testReferenceFile() {
        System.out.println("测试参照文件转换...");
        
        String testPdfPath = "doc/Test_HK4934_page10.pdf";
        String testExcelPath = "doc/Test_HK4934_page10_converted.xlsx";
        String referencePath = "doc/Test_HK4934_page10.xlsx";
        
        File testPdf = new File(testPdfPath);
        File referenceExcel = new File(referencePath);
        
        if (!testPdf.exists()) {
            System.out.println("错误: 测试PDF文件不存在 - " + testPdfPath);
            return;
        }
        
        if (referenceExcel.exists()) {
            System.out.println("参照Excel文件: " + referencePath + 
                " (大小: " + String.format("%.2f KB", referenceExcel.length() / 1024.0) + ")");
        } else {
            System.out.println("警告: 参照Excel文件不存在 - " + referencePath);
        }
        
        testConversion(testPdfPath, testExcelPath);
        
        // 比较结果
        File convertedExcel = new File(testExcelPath);
        if (convertedExcel.exists()) {
            System.out.println("\n=== 转换结果比较 ===");
            if (referenceExcel.exists()) {
                System.out.println("参照文件大小: " + String.format("%.2f KB", referenceExcel.length() / 1024.0));
            }
            System.out.println("转换文件大小: " + String.format("%.2f KB", convertedExcel.length() / 1024.0));
            System.out.println("转换文件路径: " + convertedExcel.getAbsolutePath());
            
            System.out.println("\n请手动比较两个Excel文件的内容和格式是否一致。");
        }
    }
    
    /**
     * 测试文件转换
     */
    private static void testConversion(String pdfPath, String excelPath) {
        try {
            System.out.println("\n开始转换...");
            System.out.println("PDF文件: " + pdfPath);
            System.out.println("Excel文件: " + excelPath);
            
            // 验证PDF文件
            File pdfFile = new File(pdfPath);
            if (!pdfFile.exists()) {
                System.err.println("错误: PDF文件不存在 - " + pdfPath);
                return;
            }
            
            if (!pdfFile.getName().toLowerCase().endsWith(".pdf")) {
                System.err.println("错误: 不是PDF文件 - " + pdfPath);
                return;
            }
            
            // 显示PDF信息
            long fileSize = pdfFile.length();
            System.out.println("PDF文件大小: " + String.format("%.2f KB", fileSize / 1024.0));
            
            // 执行转换
            long startTime = System.currentTimeMillis();
            PdfToExcelConverter.convertPdfToExcel(pdfPath, excelPath);
            long endTime = System.currentTimeMillis();
            
            // 验证输出文件
            File excelFile = new File(excelPath);
            if (excelFile.exists()) {
                long excelSize = excelFile.length();
                System.out.println("\n转换成功！");
                System.out.println("Excel文件: " + excelPath);
                System.out.println("Excel文件大小: " + String.format("%.2f KB", excelSize / 1024.0));
                System.out.println("转换耗时: " + (endTime - startTime) + " ms");
            } else {
                System.err.println("错误: Excel文件未生成");
            }
            
        } catch (Exception e) {
            System.err.println("转换失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
