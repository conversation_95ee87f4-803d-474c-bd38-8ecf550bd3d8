package com.zte.crm.scrapy.util;

import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.pdfbox.text.PDFTextStripperByArea;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.awt.geom.Rectangle2D;
import java.io.*;
import java.util.List;
import java.util.ArrayList;
import java.util.regex.Pattern;
import java.util.regex.Matcher;

/**
 * PDF转Excel工具类
 * 支持表格格式保持和对齐
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-13
 */
public class PdfToExcelConverter {
    
    /**
     * 将PDF文件转换为Excel文件
     * 
     * @param pdfFilePath PDF文件路径
     * @param excelFilePath Excel输出文件路径
     * @throws IOException 文件操作异常
     */
    public static void convertPdfToExcel(String pdfFilePath, String excelFilePath) throws IOException {
        convertPdfToExcel(new File(pdfFilePath), new File(excelFilePath));
    }
    
    /**
     * 将PDF文件转换为Excel文件
     * 
     * @param pdfFile PDF文件
     * @param excelFile Excel输出文件
     * @throws IOException 文件操作异常
     */
    public static void convertPdfToExcel(File pdfFile, File excelFile) throws IOException {
        try (PDDocument document = PDDocument.load(pdfFile);
             Workbook workbook = new XSSFWorkbook();
             FileOutputStream fos = new FileOutputStream(excelFile)) {
            
            // 为每一页创建一个工作表
            for (int pageIndex = 0; pageIndex < document.getNumberOfPages(); pageIndex++) {
                Sheet sheet = workbook.createSheet("Page " + (pageIndex + 1));
                processPage(document, pageIndex, sheet);
            }
            
            workbook.write(fos);
        }
    }
    
    /**
     * 处理PDF的单个页面
     *
     * @param document PDF文档
     * @param pageIndex 页面索引
     * @param sheet Excel工作表
     * @throws IOException 文件操作异常
     */
    private static void processPage(PDDocument document, int pageIndex, Sheet sheet) throws IOException {
        // 使用PDFBox提取文本并尝试识别表格结构
        extractTextAsTable(document, pageIndex, sheet);
    }
    

    
    /**
     * 当没有检测到表格时，将文本按行提取并尝试识别表格结构
     * 
     * @param document PDF文档
     * @param pageIndex 页面索引
     * @param sheet Excel工作表
     * @throws IOException 文件操作异常
     */
    private static void extractTextAsTable(PDDocument document, int pageIndex, Sheet sheet) throws IOException {
        PDFTextStripper stripper = new PDFTextStripper();
        stripper.setStartPage(pageIndex + 1);
        stripper.setEndPage(pageIndex + 1);
        String pageText = stripper.getText(document);
        
        String[] lines = pageText.split("\n");
        int rowIndex = 0;
        
        for (String line : lines) {
            line = line.trim();
            if (line.isEmpty()) continue;
            
            Row row = sheet.createRow(rowIndex++);
            
            // 尝试按空格或制表符分割
            String[] cells = splitLineIntoCells(line);
            
            for (int i = 0; i < cells.length; i++) {
                Cell cell = row.createCell(i);
                String cellText = cells[i].trim();
                
                if (isNumeric(cellText)) {
                    try {
                        double numValue = Double.parseDouble(cellText.replaceAll(",", ""));
                        cell.setCellValue(numValue);
                    } catch (NumberFormatException e) {
                        cell.setCellValue(cellText);
                    }
                } else {
                    cell.setCellValue(cellText);
                }
            }
        }
    }
    
    /**
     * 将文本行分割为单元格
     * 
     * @param line 文本行
     * @return 单元格数组
     */
    private static String[] splitLineIntoCells(String line) {
        // 首先尝试按制表符分割
        if (line.contains("\t")) {
            return line.split("\t");
        }
        
        // 然后尝试按多个空格分割
        String[] parts = line.split("\\s{2,}");
        if (parts.length > 1) {
            return parts;
        }
        
        // 最后按单个空格分割，但保留引号内的内容
        List<String> cells = new ArrayList<>();
        Pattern pattern = Pattern.compile("\"([^\"]*)\"|\\S+");
        Matcher matcher = pattern.matcher(line);
        
        while (matcher.find()) {
            if (matcher.group(1) != null) {
                cells.add(matcher.group(1));
            } else {
                cells.add(matcher.group());
            }
        }
        
        return cells.toArray(new String[0]);
    }
    
    /**
     * 判断字符串是否为数字
     * 
     * @param str 字符串
     * @return 是否为数字
     */
    private static boolean isNumeric(String str) {
        if (str == null || str.isEmpty()) {
            return false;
        }
        
        // 移除千分位分隔符
        str = str.replaceAll(",", "");
        
        try {
            Double.parseDouble(str);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }
}
