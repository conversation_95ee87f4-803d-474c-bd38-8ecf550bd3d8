package com.zte.crm.scrapy;

import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;

import java.io.File;
import java.io.IOException;

/**
 * 简单的PDF读取测试
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-13
 */
public class SimplePdfReader {
    
    public static void main(String[] args) {
        System.out.println("=== 简单PDF读取测试 ===");
        
        String testPdfPath = "doc/Test_HK4934_page10.pdf";
        
        File pdfFile = new File(testPdfPath);
        if (!pdfFile.exists()) {
            System.out.println("错误: PDF文件不存在 - " + testPdfPath);
            return;
        }
        
        try {
            System.out.println("PDF文件: " + testPdfPath);
            System.out.println("文件大小: " + String.format("%.2f KB", pdfFile.length() / 1024.0));
            
            // 尝试读取PDF
            try (PDDocument document = PDDocument.load(pdfFile)) {
                int pageCount = document.getNumberOfPages();
                System.out.println("页数: " + pageCount);
                
                // 提取第一页的文本
                PDFTextStripper stripper = new PDFTextStripper();
                stripper.setStartPage(1);
                stripper.setEndPage(1);
                String text = stripper.getText(document);
                
                System.out.println("\n第一页文本内容:");
                System.out.println("==================================================");
                System.out.println(text);
                System.out.println("==================================================");
                
                // 分析文本结构
                String[] lines = text.split("\n");
                System.out.println("\n文本分析:");
                System.out.println("总行数: " + lines.length);
                
                int nonEmptyLines = 0;
                for (String line : lines) {
                    if (!line.trim().isEmpty()) {
                        nonEmptyLines++;
                    }
                }
                System.out.println("非空行数: " + nonEmptyLines);
                
                System.out.println("\n前10行内容:");
                for (int i = 0; i < Math.min(10, lines.length); i++) {
                    System.out.println((i + 1) + ": " + lines[i]);
                }
                
            }
            
        } catch (IOException e) {
            System.err.println("读取PDF失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
