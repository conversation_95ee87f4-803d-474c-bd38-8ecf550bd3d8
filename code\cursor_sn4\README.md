# PDF转Excel工具类

这是一个增强版的PDF转Excel转换工具，特别关注表格格式保持和对齐。

## 项目结构

```
code/cursor_sn4/
├── EnhancedPdfToExcelConverter.java    # 增强版PDF转Excel工具类
├── IPdfToExcelConverterService.java    # 服务接口
├── PdfToExcelConverterServiceImpl.java # 服务实现类
├── PdfToExcelConverterTest.java        # 测试类
├── PdfToExcelExample.java              # 使用示例
└── README.md                           # 说明文档
```

## 功能特点

### 增强的表格处理
- **智能表格检测**: 使用Tabula库进行准确的表格边界识别
- **格式保持**: 保持原PDF中的表格对齐和布局
- **合并单元格支持**: 自动检测和处理跨行跨列的单元格
- **数据类型识别**: 自动识别数字、货币、日期等数据类型

### 多种转换模式
- **高质量模式**: 最大程度保持原格式，适合重要文档
- **快速模式**: 牺牲部分质量换取处理速度
- **默认模式**: 平衡质量和速度的中等配置

### 样式和格式
- **标题样式**: 自动识别和格式化表头
- **边框样式**: 保持表格边框和分隔线
- **数字格式**: 自动应用数字格式和千分位分隔符
- **自动列宽**: 根据内容自动调整列宽

## 依赖要求

确保项目包含以下依赖：

```xml
<!-- Apache POI for Excel processing -->
<dependency>
    <groupId>org.apache.poi</groupId>
    <artifactId>poi-ooxml</artifactId>
    <version>5.2.3</version>
</dependency>

<!-- Apache PDFBox for PDF processing -->
<dependency>
    <groupId>org.apache.pdfbox</groupId>
    <artifactId>pdfbox</artifactId>
    <version>2.0.29</version>
</dependency>

<!-- Tabula for PDF table extraction -->
<dependency>
    <groupId>technology.tabula</groupId>
    <artifactId>tabula-java</artifactId>
    <version>1.0.5</version>
</dependency>

<!-- SLF4J for logging -->
<dependency>
    <groupId>org.slf4j</groupId>
    <artifactId>slf4j-api</artifactId>
    <version>1.7.36</version>
</dependency>
```

## 使用方法

### 1. 直接使用工具类

```java
import com.alix.pdf2excel.util.EnhancedPdfToExcelConverter;

// 简单转换
File pdfFile = new File("input.pdf");
File excelFile = new File("output.xlsx");
EnhancedPdfToExcelConverter.convertPdfToExcel(pdfFile, excelFile);
```

### 2. 使用服务接口（推荐）

```java
import com.alix.pdf2excel.service.IPdfToExcelConverterService;
import com.alix.pdf2excel.service.impl.PdfToExcelConverterServiceImpl;

IPdfToExcelConverterService service = new PdfToExcelConverterServiceImpl();

// 基本转换
ConversionResult result = service.convertPdfToExcel("input.pdf", "output.xlsx");
System.out.println(result.toString());

// 使用高质量配置
ConversionConfig config = ConversionConfig.getHighQuality();
ConversionResult result = service.convertPdfToExcel("input.pdf", "output.xlsx", config);
```

### 3. 批量转换

```java
// 批量转换目录中的所有PDF文件
List<ConversionResult> results = service.batchConvertPdfToExcel(
    "input_directory/", 
    "output_directory/"
);

// 打印统计信息
if (service instanceof PdfToExcelConverterServiceImpl) {
    PdfToExcelConverterServiceImpl impl = (PdfToExcelConverterServiceImpl) service;
    String statistics = impl.getConversionStatistics(results);
    System.out.println(statistics);
}
```

### 4. 自定义配置

```java
ConversionConfig config = new ConversionConfig();
config.setPreserveTableAlignment(true);    // 保持表格对齐
config.setAutoDetectNumbers(true);          // 自动检测数字
config.setMergeCells(true);                 // 支持合并单元格
config.setApplyStyles(true);                // 应用样式
config.setAutoSizeColumns(true);            // 自动调整列宽
config.setSheetNamePrefix("页面_");          // 工作表名前缀

ConversionResult result = service.convertPdfToExcel(pdfFile, excelFile, config);
```

## 配置选项说明

| 配置项 | 说明 | 默认值 |
|--------|------|---------|
| `preserveTableAlignment` | 保持表格对齐 | true |
| `autoDetectNumbers` | 自动检测数字类型 | true |
| `mergeCells` | 支持合并单元格 | true |
| `applyStyles` | 应用格式样式 | true |
| `autoSizeColumns` | 自动调整列宽 | true |
| `maxColumnWidth` | 最大列宽限制 | 15000 |
| `createSeparateSheets` | 为每页创建工作表 | true |
| `sheetNamePrefix` | 工作表名前缀 | "Page " |
| `includeEmptyRows` | 包含空行 | false |
| `detectHeaders` | 检测标题行 | true |

## 预设配置

### 高质量配置
```java
ConversionConfig.getHighQuality()
```
- 最大程度保持原格式
- 适合重要文档和复杂表格
- 处理时间较长

### 快速配置
```java
ConversionConfig.getFast()
```
- 牺牲部分质量换取速度
- 适合大批量处理
- 基本格式保持

### 默认配置
```java
ConversionConfig.getDefault()
```
- 平衡质量和速度
- 适合大多数使用场景
- 推荐的通用配置

## 测试和验证

运行测试类验证功能：

```java
// 运行所有测试
java com.alix.pdf2excel.test.PdfToExcelConverterTest

// 或查看使用示例
java com.alix.pdf2excel.example.PdfToExcelExample
```

## 转换质量优化建议

1. **表格对齐问题**：
   - 确保PDF中的表格有清晰的边界线
   - 避免使用只有空格分隔的伪表格
   - 复杂表格建议使用高质量配置

2. **数据类型识别**：
   - 数字格式应保持一致（如千分位分隔符）
   - 日期格式建议使用标准格式
   - 货币符号应明确标识

3. **性能优化**：
   - 大文件建议分页处理
   - 批量转换时考虑使用快速配置
   - 合理设置最大列宽避免过宽

## 常见问题

### Q: 转换后的表格对齐不正确？
A: 
- 检查原PDF是否为真正的表格结构
- 尝试使用高质量配置
- 确保PDF文件质量良好

### Q: 数字没有被正确识别？
A: 
- 检查数字格式是否标准
- 确保启用了`autoDetectNumbers`配置
- 可能需要手动后处理特殊格式

### Q: 转换速度很慢？
A: 
- 使用快速配置模式
- 考虑关闭复杂功能如合并单元格
- 分批处理大文件

### Q: 某些页面转换失败？
A: 
- 检查PDF页面是否损坏
- 查看日志获取详细错误信息
- 尝试不同的转换配置

## 版本信息

- 当前版本：2.0.0
- 支持的输入格式：PDF
- 支持的输出格式：XLSX, XLS
- Java版本要求：1.8+

## 参考文件

本工具的开发参考了以下文件的转换效果：
- `doc/Test_HK4934_page10.pdf` - 原始PDF文件
- `doc/Test_HK4934_page10.xlsx` - 参考转换结果

可以使用这些文件来验证和比较转换质量。
