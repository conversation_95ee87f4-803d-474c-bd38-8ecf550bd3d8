package com.alix.pdf2excel.util;

import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFColor;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import technology.tabula.*;
import technology.tabula.extractors.SpreadsheetExtractionAlgorithm;
import technology.tabula.extractors.BasicExtractionAlgorithm;

import java.io.*;
import java.util.*;
import java.util.regex.Pattern;
import java.util.regex.Matcher;
import java.awt.geom.Rectangle2D;

/**
 * 增强版PDF转Excel工具类
 * 重点关注表格格式保持和对齐
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-13
 */
public class EnhancedPdfToExcelConverter {
    
    // 数字模式
    private static final Pattern NUMBER_PATTERN = Pattern.compile("^-?\\d+([.,]\\d+)*%?$");
    // 货币模式
    private static final Pattern CURRENCY_PATTERN = Pattern.compile("^[\\$¥€£]?\\s*-?\\d+([.,]\\d+)*\\s*[\\$¥€£]?$");
    // 日期模式
    private static final Pattern DATE_PATTERN = Pattern.compile("^\\d{1,2}[/-]\\d{1,2}[/-]\\d{2,4}$|^\\d{4}[/-]\\d{1,2}[/-]\\d{1,2}$");
    
    /**
     * 表格单元格信息类
     */
    public static class CellInfo {
        private String text;
        private Rectangle2D bounds;
        private int rowSpan = 1;
        private int colSpan = 1;
        private CellType cellType = CellType.STRING;
        private Object value;
        
        public CellInfo(String text, Rectangle2D bounds) {
            this.text = text;
            this.bounds = bounds;
            this.value = text;
            identifyCellType();
        }
        
        private void identifyCellType() {
            if (text == null || text.trim().isEmpty()) {
                this.cellType = CellType.BLANK;
                return;
            }
            
            String trimmed = text.trim();
            
            // 检查是否为数字
            if (NUMBER_PATTERN.matcher(trimmed).matches() || CURRENCY_PATTERN.matcher(trimmed).matches()) {
                try {
                    // 清理数字字符串
                    String cleanNumber = trimmed.replaceAll("[\\$¥€£%,\\s]", "");
                    double numValue = Double.parseDouble(cleanNumber);
                    this.value = numValue;
                    this.cellType = CellType.NUMERIC;
                } catch (NumberFormatException e) {
                    this.cellType = CellType.STRING;
                }
            } else if (DATE_PATTERN.matcher(trimmed).matches()) {
                this.cellType = CellType.STRING; // 日期暂时作为字符串处理
            } else {
                this.cellType = CellType.STRING;
            }
        }
        
        // Getters and setters
        public String getText() { return text; }
        public Rectangle2D getBounds() { return bounds; }
        public int getRowSpan() { return rowSpan; }
        public int getColSpan() { return colSpan; }
        public CellType getCellType() { return cellType; }
        public Object getValue() { return value; }
        public void setRowSpan(int rowSpan) { this.rowSpan = rowSpan; }
        public void setColSpan(int colSpan) { this.colSpan = colSpan; }
    }
    
    /**
     * 将PDF文件转换为Excel文件
     * 
     * @param pdfFilePath PDF文件路径
     * @param excelFilePath Excel输出文件路径
     * @throws IOException 文件操作异常
     */
    public static void convertPdfToExcel(String pdfFilePath, String excelFilePath) throws IOException {
        convertPdfToExcel(new File(pdfFilePath), new File(excelFilePath));
    }
    
    /**
     * 将PDF文件转换为Excel文件
     * 
     * @param pdfFile PDF文件
     * @param excelFile Excel输出文件
     * @throws IOException 文件操作异常
     */
    public static void convertPdfToExcel(File pdfFile, File excelFile) throws IOException {
        try (PDDocument document = PDDocument.load(pdfFile);
             XSSFWorkbook workbook = new XSSFWorkbook();
             FileOutputStream fos = new FileOutputStream(excelFile)) {
            
            // 为每一页创建一个工作表
            for (int pageIndex = 0; pageIndex < document.getNumberOfPages(); pageIndex++) {
                XSSFSheet sheet = workbook.createSheet("Page " + (pageIndex + 1));
                processPageEnhanced(document, pageIndex, sheet, workbook);
            }
            
            workbook.write(fos);
        }
    }
    
    /**
     * 增强版页面处理方法
     * 
     * @param document PDF文档
     * @param pageIndex 页面索引
     * @param sheet Excel工作表
     * @param workbook Excel工作簿
     * @throws IOException 文件操作异常
     */
    private static void processPageEnhanced(PDDocument document, int pageIndex, XSSFSheet sheet, XSSFWorkbook workbook) throws IOException {
        Page page = new ObjectExtractor(document).extract(pageIndex + 1);
        
        // 尝试使用Spreadsheet算法提取表格
        SpreadsheetExtractionAlgorithm spreadsheetExtractor = new SpreadsheetExtractionAlgorithm();
        List<Table> tables = spreadsheetExtractor.extract(page);
        
        // 如果没有检测到表格，使用Basic算法
        if (tables.isEmpty()) {
            BasicExtractionAlgorithm basicExtractor = new BasicExtractionAlgorithm();
            tables = basicExtractor.extract(page);
        }
        
        if (tables.isEmpty()) {
            // 如果仍然没有表格，尝试智能文本提取
            extractTextAsTableEnhanced(document, pageIndex, sheet, workbook);
        } else {
            // 处理检测到的表格
            int currentRow = 0;
            for (Table table : tables) {
                currentRow = writeTableToSheetEnhanced(table, sheet, workbook, currentRow);
                currentRow += 2; // 表格之间留空行
            }
        }
        
        // 自动调整列宽
        autoSizeColumns(sheet);
    }
    
    /**
     * 增强版表格写入方法
     * 
     * @param table 表格对象
     * @param sheet Excel工作表
     * @param workbook Excel工作簿
     * @param startRow 起始行
     * @return 下一个可用行号
     */
    private static int writeTableToSheetEnhanced(Table table, XSSFSheet sheet, XSSFWorkbook workbook, int startRow) {
        List<List<RectangularTextContainer>> rows = table.getRows();
        
        // 创建样式
        CellStyle headerStyle = createHeaderStyle(workbook);
        CellStyle dataStyle = createDataStyle(workbook);
        CellStyle numberStyle = createNumberStyle(workbook);
        
        // 分析表格结构，检测合并单元格
        List<List<CellInfo>> cellMatrix = analyzeCellStructure(rows);
        
        for (int i = 0; i < cellMatrix.size(); i++) {
            Row row = sheet.createRow(startRow + i);
            List<CellInfo> cellInfos = cellMatrix.get(i);
            
            for (int j = 0; j < cellInfos.size(); j++) {
                CellInfo cellInfo = cellInfos.get(j);
                if (cellInfo == null) continue; // 合并单元格的一部分
                
                Cell cell = row.createCell(j);
                
                // 设置单元格值
                switch (cellInfo.getCellType()) {
                    case NUMERIC:
                        cell.setCellValue((Double) cellInfo.getValue());
                        cell.setCellStyle(numberStyle);
                        break;
                    case BLANK:
                        cell.setCellValue("");
                        cell.setCellStyle(dataStyle);
                        break;
                    default:
                        cell.setCellValue(cellInfo.getText());
                        // 第一行通常是标题
                        cell.setCellStyle(i == 0 ? headerStyle : dataStyle);
                        break;
                }
                
                // 处理合并单元格
                if (cellInfo.getRowSpan() > 1 || cellInfo.getColSpan() > 1) {
                    int lastRow = startRow + i + cellInfo.getRowSpan() - 1;
                    int lastCol = j + cellInfo.getColSpan() - 1;
                    sheet.addMergedRegion(new CellRangeAddress(startRow + i, lastRow, j, lastCol));
                }
            }
        }
        
        return startRow + cellMatrix.size();
    }
    
    /**
     * 分析单元格结构，检测可能的合并单元格
     * 
     * @param rows 表格行数据
     * @return 单元格信息矩阵
     */
    private static List<List<CellInfo>> analyzeCellStructure(List<List<RectangularTextContainer>> rows) {
        List<List<CellInfo>> cellMatrix = new ArrayList<>();
        
        for (List<RectangularTextContainer> row : rows) {
            List<CellInfo> cellInfoRow = new ArrayList<>();
            
            for (RectangularTextContainer cell : row) {
                CellInfo cellInfo = new CellInfo(cell.getText().trim(), cell.getBounds());
                
                // 分析可能的合并单元格（基于文本长度和边界）
                if (cellInfo.getText().length() > 50) {
                    // 长文本可能跨多列
                    cellInfo.setColSpan(Math.min(3, cellInfo.getText().length() / 20));
                }
                
                cellInfoRow.add(cellInfo);
            }
            
            cellMatrix.add(cellInfoRow);
        }
        
        // 标准化行的列数
        int maxCols = cellMatrix.stream().mapToInt(List::size).max().orElse(0);
        for (List<CellInfo> row : cellMatrix) {
            while (row.size() < maxCols) {
                row.add(new CellInfo("", new Rectangle2D.Double()));
            }
        }
        
        return cellMatrix;
    }
    
    /**
     * 增强版文本提取方法
     * 
     * @param document PDF文档
     * @param pageIndex 页面索引
     * @param sheet Excel工作表
     * @param workbook Excel工作簿
     * @throws IOException 文件操作异常
     */
    private static void extractTextAsTableEnhanced(PDDocument document, int pageIndex, XSSFSheet sheet, XSSFWorkbook workbook) throws IOException {
        PDFTextStripper stripper = new PDFTextStripper();
        stripper.setStartPage(pageIndex + 1);
        stripper.setEndPage(pageIndex + 1);
        String pageText = stripper.getText(document);
        
        String[] lines = pageText.split("\n");
        int rowIndex = 0;
        
        // 创建样式
        CellStyle headerStyle = createHeaderStyle(workbook);
        CellStyle dataStyle = createDataStyle(workbook);
        CellStyle numberStyle = createNumberStyle(workbook);
        
        // 分析文本结构，尝试识别表格模式
        List<String[]> tableData = analyzeTextStructure(lines);
        
        for (String[] rowData : tableData) {
            if (rowData.length == 0) continue;
            
            Row row = sheet.createRow(rowIndex);
            
            for (int i = 0; i < rowData.length; i++) {
                Cell cell = row.createCell(i);
                String cellText = rowData[i].trim();
                
                CellInfo cellInfo = new CellInfo(cellText, new Rectangle2D.Double());
                
                switch (cellInfo.getCellType()) {
                    case NUMERIC:
                        cell.setCellValue((Double) cellInfo.getValue());
                        cell.setCellStyle(numberStyle);
                        break;
                    case BLANK:
                        cell.setCellValue("");
                        cell.setCellStyle(dataStyle);
                        break;
                    default:
                        cell.setCellValue(cellText);
                        cell.setCellStyle(rowIndex == 0 ? headerStyle : dataStyle);
                        break;
                }
            }
            
            rowIndex++;
        }
        
        // 自动调整列宽
        autoSizeColumns(sheet);
    }
    
    /**
     * 分析文本结构，智能识别表格
     * 
     * @param lines 文本行数组
     * @return 表格数据
     */
    private static List<String[]> analyzeTextStructure(String[] lines) {
        List<String[]> tableData = new ArrayList<>();
        List<String> potentialTableLines = new ArrayList<>();
        
        for (String line : lines) {
            line = line.trim();
            if (line.isEmpty()) {
                if (!potentialTableLines.isEmpty()) {
                    // 处理累积的表格行
                    processTableBlock(potentialTableLines, tableData);
                    potentialTableLines.clear();
                }
                continue;
            }
            
            // 检查是否可能是表格行
            if (isPotentialTableLine(line)) {
                potentialTableLines.add(line);
            } else {
                if (!potentialTableLines.isEmpty()) {
                    processTableBlock(potentialTableLines, tableData);
                    potentialTableLines.clear();
                }
                // 非表格行作为单独的行处理
                tableData.add(new String[]{line});
            }
        }
        
        // 处理剩余的表格行
        if (!potentialTableLines.isEmpty()) {
            processTableBlock(potentialTableLines, tableData);
        }
        
        return tableData;
    }
    
    /**
     * 检查是否可能是表格行
     * 
     * @param line 文本行
     * @return 是否可能是表格行
     */
    private static boolean isPotentialTableLine(String line) {
        // 包含多个空格分隔的项目
        String[] parts = line.split("\\s{2,}");
        if (parts.length > 1) return true;
        
        // 包含制表符
        if (line.contains("\t")) return true;
        
        // 包含数字和文本的混合
        boolean hasNumber = NUMBER_PATTERN.matcher(line).find();
        boolean hasText = line.matches(".*[a-zA-Z\u4e00-\u9fa5].*");
        
        return hasNumber && hasText;
    }
    
    /**
     * 处理表格块
     * 
     * @param tableLines 表格行列表
     * @param tableData 输出表格数据
     */
    private static void processTableBlock(List<String> tableLines, List<String[]> tableData) {
        int maxColumns = 0;
        List<String[]> blockData = new ArrayList<>();
        
        // 先分析所有行，确定最大列数
        for (String line : tableLines) {
            String[] cells = splitLineIntoCellsEnhanced(line);
            blockData.add(cells);
            maxColumns = Math.max(maxColumns, cells.length);
        }
        
        // 标准化列数
        for (String[] row : blockData) {
            if (row.length < maxColumns) {
                String[] normalizedRow = Arrays.copyOf(row, maxColumns);
                for (int i = row.length; i < maxColumns; i++) {
                    normalizedRow[i] = "";
                }
                tableData.add(normalizedRow);
            } else {
                tableData.add(row);
            }
        }
    }
    
    /**
     * 增强版文本行分割方法
     * 
     * @param line 文本行
     * @return 单元格数组
     */
    private static String[] splitLineIntoCellsEnhanced(String line) {
        // 首先尝试按制表符分割
        if (line.contains("\t")) {
            return line.split("\t");
        }
        
        // 尝试按多个空格分割
        String[] parts = line.split("\\s{2,}");
        if (parts.length > 1) {
            return parts;
        }
        
        // 尝试识别固定宽度的列
        List<String> cells = new ArrayList<>();
        
        // 使用正则表达式识别不同类型的数据块
        Pattern pattern = Pattern.compile("(\\S+(?:\\s+\\S+)*?)(?=\\s{2,}|$)");
        Matcher matcher = pattern.matcher(line);
        
        while (matcher.find()) {
            cells.add(matcher.group(1).trim());
        }
        
        if (cells.isEmpty()) {
            // 最后回退到单个空格分割
            return line.split("\\s+");
        }
        
        return cells.toArray(new String[0]);
    }
    
    /**
     * 创建标题样式
     * 
     * @param workbook Excel工作簿
     * @return 标题样式
     */
    private static CellStyle createHeaderStyle(XSSFWorkbook workbook) {
        XSSFCellStyle style = workbook.createCellStyle();
        
        // 设置字体
        Font font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 12);
        style.setFont(font);
        
        // 设置背景色
        style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        
        // 设置边框
        style.setBorderTop(BorderStyle.MEDIUM);
        style.setBorderBottom(BorderStyle.MEDIUM);
        style.setBorderLeft(BorderStyle.MEDIUM);
        style.setBorderRight(BorderStyle.MEDIUM);
        
        // 设置对齐
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        
        return style;
    }
    
    /**
     * 创建数据样式
     * 
     * @param workbook Excel工作簿
     * @return 数据样式
     */
    private static CellStyle createDataStyle(XSSFWorkbook workbook) {
        XSSFCellStyle style = workbook.createCellStyle();
        
        // 设置边框
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        
        // 设置对齐
        style.setAlignment(HorizontalAlignment.LEFT);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        
        return style;
    }
    
    /**
     * 创建数字样式
     * 
     * @param workbook Excel工作簿
     * @return 数字样式
     */
    private static CellStyle createNumberStyle(XSSFWorkbook workbook) {
        XSSFCellStyle style = workbook.createCellStyle();
        
        // 设置边框
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        
        // 设置对齐
        style.setAlignment(HorizontalAlignment.RIGHT);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        
        // 设置数字格式
        style.setDataFormat(workbook.createDataFormat().getFormat("#,##0.00"));
        
        return style;
    }
    
    /**
     * 自动调整列宽
     * 
     * @param sheet Excel工作表
     */
    private static void autoSizeColumns(XSSFSheet sheet) {
        if (sheet.getPhysicalNumberOfRows() > 0) {
            Row firstRow = sheet.getRow(0);
            if (firstRow != null) {
                int lastColumn = firstRow.getLastCellNum();
                for (int i = 0; i < lastColumn; i++) {
                    sheet.autoSizeColumn(i);
                    // 限制最大列宽
                    int columnWidth = sheet.getColumnWidth(i);
                    if (columnWidth > 15000) {
                        sheet.setColumnWidth(i, 15000);
                    }
                }
            }
        }
    }
    
    /**
     * 验证PDF文件
     * 
     * @param pdfFile PDF文件
     * @return 是否有效
     */
    public static boolean validatePdfFile(File pdfFile) {
        if (pdfFile == null || !pdfFile.exists() || !pdfFile.isFile()) {
            return false;
        }
        
        if (!pdfFile.getName().toLowerCase().endsWith(".pdf")) {
            return false;
        }
        
        try (PDDocument document = PDDocument.load(pdfFile)) {
            return document.getNumberOfPages() > 0;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 获取PDF页数
     * 
     * @param pdfFile PDF文件
     * @return 页数
     * @throws IOException 文件操作异常
     */
    public static int getPdfPageCount(File pdfFile) throws IOException {
        try (PDDocument document = PDDocument.load(pdfFile)) {
            return document.getNumberOfPages();
        }
    }
}
