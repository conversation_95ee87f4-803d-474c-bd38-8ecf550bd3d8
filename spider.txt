sequenceDiagram
    participant Service as ScrapyNewsTitleCommonServiceImpl
    participant <PERSON> as UnicomReportSpider
    participant <PERSON><PERSON><PERSON><PERSON> as JobProcessorByUnicomReport
    participant Detail<PERSON><PERSON><PERSON> as UnicomReportDetailSpider
    participant <PERSON>ailProc as JobDetailProcessorByUnicomReport
    participant <PERSON><PERSON><PERSON> as PipelineDetail

    Service ->> Spider: doSpider(initDataReq)
    Spider ->> Spider: buildRequestList()
    Spider ->> ListProc: Spider.run()
    ListProc ->> ListProc: parse list JSON
    ListProc ->> DetailSpider: buildSpider(detailRequests)
    DetailSpider ->> DetailProc: Spider.run()
    DetailProc ->> Pipeline: store ScrapyNewsContent
