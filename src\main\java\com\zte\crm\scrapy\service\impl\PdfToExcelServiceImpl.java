package com.zte.crm.scrapy.service.impl;

import com.zte.crm.scrapy.service.IPdfToExcelService;
import com.zte.crm.scrapy.util.PdfToExcelConverter;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.springframework.stereotype.Service;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * PDF转Excel服务实现类
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-13
 */
@Slf4j
@Service
public class PdfToExcelServiceImpl implements IPdfToExcelService {
    
    @Override
    public String convertPdfToExcel(String pdfFilePath, String excelFilePath) throws IOException {
        log.info("开始转换PDF文件: {} -> {}", pdfFilePath, excelFilePath);
        
        File pdfFile = new File(pdfFilePath);
        File excelFile = new File(excelFilePath);
        
        return convertPdfToExcel(pdfFile, excelFile);
    }
    
    @Override
    public String convertPdfToExcel(File pdfFile, File excelFile) throws IOException {
        long startTime = System.currentTimeMillis();
        
        // 验证PDF文件
        if (!validatePdfFile(pdfFile)) {
            throw new IllegalArgumentException("无效的PDF文件: " + pdfFile.getAbsolutePath());
        }
        
        // 确保输出目录存在
        File parentDir = excelFile.getParentFile();
        if (parentDir != null && !parentDir.exists()) {
            parentDir.mkdirs();
        }
        
        try {
            // 执行转换
            PdfToExcelConverter.convertPdfToExcel(pdfFile, excelFile);
            
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            // 获取文件信息
            int pageCount = getPdfPageCount(pdfFile);
            long pdfSize = pdfFile.length();
            long excelSize = excelFile.length();
            
            String result = String.format(
                "转换成功！\n" +
                "PDF文件: %s (%.2f KB, %d页)\n" +
                "Excel文件: %s (%.2f KB)\n" +
                "耗时: %d ms",
                pdfFile.getName(), pdfSize / 1024.0, pageCount,
                excelFile.getName(), excelSize / 1024.0,
                duration
            );
            
            log.info("PDF转Excel完成: {}", result);
            return result;
            
        } catch (Exception e) {
            log.error("PDF转Excel失败: {}", e.getMessage(), e);
            throw new IOException("转换失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public String convertPdfToExcel(InputStream pdfInputStream, String excelFilePath) throws IOException {
        // 创建临时PDF文件
        Path tempPdfPath = Files.createTempFile("temp_pdf_", ".pdf");
        File tempPdfFile = tempPdfPath.toFile();
        
        try {
            // 将输入流写入临时文件
            try (FileOutputStream fos = new FileOutputStream(tempPdfFile)) {
                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = pdfInputStream.read(buffer)) != -1) {
                    fos.write(buffer, 0, bytesRead);
                }
            }
            
            // 执行转换
            File excelFile = new File(excelFilePath);
            return convertPdfToExcel(tempPdfFile, excelFile);
            
        } finally {
            // 清理临时文件
            try {
                Files.deleteIfExists(tempPdfPath);
            } catch (IOException e) {
                log.warn("删除临时文件失败: {}", tempPdfPath, e);
            }
        }
    }
    
    @Override
    public boolean validatePdfFile(File pdfFile) {
        if (pdfFile == null || !pdfFile.exists() || !pdfFile.isFile()) {
            return false;
        }
        
        if (!pdfFile.getName().toLowerCase().endsWith(".pdf")) {
            return false;
        }
        
        // 尝试打开PDF文件验证格式
        try (PDDocument document = PDDocument.load(pdfFile)) {
            return document.getNumberOfPages() > 0;
        } catch (Exception e) {
            log.warn("PDF文件验证失败: {}", pdfFile.getAbsolutePath(), e);
            return false;
        }
    }
    
    @Override
    public int getPdfPageCount(File pdfFile) throws IOException {
        try (PDDocument document = PDDocument.load(pdfFile)) {
            return document.getNumberOfPages();
        }
    }
}
