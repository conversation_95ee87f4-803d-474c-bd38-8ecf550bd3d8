package com.zte.crm.scrapy;

import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;

/**
 * 简单的XLS生成测试（使用老格式避免XML问题）
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-13
 */
public class SimpleXlsTest {
    
    public static void main(String[] args) {
        System.out.println("=== 简单XLS生成测试 ===");
        
        try {
            String outputPath = "doc/sample_output.xls";
            
            System.out.println("生成示例XLS文件...");
            System.out.println("输出路径: " + outputPath);
            
            // 创建工作簿
            Workbook workbook = new HSSFWorkbook();
            Sheet sheet = workbook.createSheet("示例数据");
            
            // 创建样式
            CellStyle headerStyle = workbook.createCellStyle();
            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            headerStyle.setFont(headerFont);
            headerStyle.setFillForegroundColor(IndexedColors.LIGHT_BLUE.getIndex());
            headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            
            // 创建表头
            Row headerRow = sheet.createRow(0);
            String[] headers = {"项目名称", "项目编号", "金额", "状态", "日期"};
            
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
            }
            
            // 创建数据行
            String[][] data = {
                {"测试项目A", "PRJ001", "1000000", "进行中", "2025-01-13"},
                {"测试项目B", "PRJ002", "2500000", "已完成", "2025-01-12"},
                {"测试项目C", "PRJ003", "800000", "待开始", "2025-01-14"},
                {"测试项目D", "PRJ004", "1500000", "进行中", "2025-01-11"},
                {"测试项目E", "PRJ005", "3200000", "已完成", "2025-01-10"}
            };
            
            for (int i = 0; i < data.length; i++) {
                Row row = sheet.createRow(i + 1);
                for (int j = 0; j < data[i].length; j++) {
                    Cell cell = row.createCell(j);
                    
                    // 如果是金额列，设置为数字
                    if (j == 2) {
                        try {
                            double amount = Double.parseDouble(data[i][j]);
                            cell.setCellValue(amount);
                        } catch (NumberFormatException e) {
                            cell.setCellValue(data[i][j]);
                        }
                    } else {
                        cell.setCellValue(data[i][j]);
                    }
                }
            }
            
            // 自动调整列宽
            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
            }
            
            // 写入文件
            try (FileOutputStream fos = new FileOutputStream(outputPath)) {
                workbook.write(fos);
            }
            
            workbook.close();
            
            // 验证文件
            File outputFile = new File(outputPath);
            if (outputFile.exists()) {
                System.out.println("XLS文件生成成功！");
                System.out.println("文件大小: " + String.format("%.2f KB", outputFile.length() / 1024.0));
                System.out.println("文件路径: " + outputFile.getAbsolutePath());
                
                // 创建一个更复杂的示例
                createComplexExample();
                
            } else {
                System.err.println("XLS文件生成失败！");
            }
            
        } catch (Exception e) {
            System.err.println("生成XLS失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 创建一个更复杂的示例，模拟PDF转Excel的效果
     */
    private static void createComplexExample() throws IOException {
        System.out.println("\n创建复杂示例...");
        
        String outputPath = "doc/complex_sample.xls";
        
        Workbook workbook = new HSSFWorkbook();
        Sheet sheet = workbook.createSheet("财务报表");
        
        // 创建标题
        Row titleRow = sheet.createRow(0);
        Cell titleCell = titleRow.createCell(0);
        titleCell.setCellValue("2025年度财务报表");
        
        CellStyle titleStyle = workbook.createCellStyle();
        Font titleFont = workbook.createFont();
        titleFont.setBold(true);
        titleFont.setFontHeightInPoints((short) 16);
        titleStyle.setFont(titleFont);
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        titleCell.setCellStyle(titleStyle);
        
        // 合并标题单元格
        sheet.addMergedRegion(new org.apache.poi.ss.util.CellRangeAddress(0, 0, 0, 4));
        
        // 创建表头
        Row headerRow = sheet.createRow(2);
        String[] headers = {"收入类型", "第一季度", "第二季度", "第三季度", "第四季度"};
        
        CellStyle headerStyle = workbook.createCellStyle();
        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerStyle.setFont(headerFont);
        headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        headerStyle.setBorderBottom(BorderStyle.THIN);
        headerStyle.setBorderTop(BorderStyle.THIN);
        headerStyle.setBorderRight(BorderStyle.THIN);
        headerStyle.setBorderLeft(BorderStyle.THIN);
        
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }
        
        // 创建数据
        String[][] data = {
            {"产品销售", "1200000", "1350000", "1180000", "1420000"},
            {"服务收入", "800000", "920000", "850000", "980000"},
            {"其他收入", "150000", "180000", "160000", "200000"}
        };
        
        CellStyle dataStyle = workbook.createCellStyle();
        dataStyle.setBorderBottom(BorderStyle.THIN);
        dataStyle.setBorderTop(BorderStyle.THIN);
        dataStyle.setBorderRight(BorderStyle.THIN);
        dataStyle.setBorderLeft(BorderStyle.THIN);
        
        CellStyle numberStyle = workbook.createCellStyle();
        numberStyle.setBorderBottom(BorderStyle.THIN);
        numberStyle.setBorderTop(BorderStyle.THIN);
        numberStyle.setBorderRight(BorderStyle.THIN);
        numberStyle.setBorderLeft(BorderStyle.THIN);
        numberStyle.setDataFormat(workbook.createDataFormat().getFormat("#,##0"));
        
        for (int i = 0; i < data.length; i++) {
            Row row = sheet.createRow(i + 3);
            for (int j = 0; j < data[i].length; j++) {
                Cell cell = row.createCell(j);
                
                if (j == 0) {
                    cell.setCellValue(data[i][j]);
                    cell.setCellStyle(dataStyle);
                } else {
                    try {
                        double amount = Double.parseDouble(data[i][j]);
                        cell.setCellValue(amount);
                        cell.setCellStyle(numberStyle);
                    } catch (NumberFormatException e) {
                        cell.setCellValue(data[i][j]);
                        cell.setCellStyle(dataStyle);
                    }
                }
            }
        }
        
        // 添加合计行
        Row totalRow = sheet.createRow(data.length + 3);
        Cell totalLabelCell = totalRow.createCell(0);
        totalLabelCell.setCellValue("合计");
        
        CellStyle totalStyle = workbook.createCellStyle();
        Font totalFont = workbook.createFont();
        totalFont.setBold(true);
        totalStyle.setFont(totalFont);
        totalStyle.setBorderBottom(BorderStyle.THICK);
        totalStyle.setBorderTop(BorderStyle.THICK);
        totalStyle.setBorderRight(BorderStyle.THICK);
        totalStyle.setBorderLeft(BorderStyle.THICK);
        totalStyle.setDataFormat(workbook.createDataFormat().getFormat("#,##0"));
        
        totalLabelCell.setCellStyle(totalStyle);
        
        // 计算合计
        for (int j = 1; j < headers.length; j++) {
            Cell totalCell = totalRow.createCell(j);
            String formula = String.format("SUM(%s%d:%s%d)", 
                getColumnName(j), 4, getColumnName(j), data.length + 3);
            totalCell.setCellFormula(formula);
            totalCell.setCellStyle(totalStyle);
        }
        
        // 自动调整列宽
        for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }
        
        // 写入文件
        try (FileOutputStream fos = new FileOutputStream(outputPath)) {
            workbook.write(fos);
        }
        
        workbook.close();
        
        File outputFile = new File(outputPath);
        System.out.println("复杂示例生成成功！");
        System.out.println("文件路径: " + outputFile.getAbsolutePath());
        System.out.println("文件大小: " + String.format("%.2f KB", outputFile.length() / 1024.0));
    }
    
    /**
     * 获取Excel列名（A, B, C...）
     */
    private static String getColumnName(int columnIndex) {
        StringBuilder columnName = new StringBuilder();
        while (columnIndex >= 0) {
            columnName.insert(0, (char) ('A' + columnIndex % 26));
            columnIndex = columnIndex / 26 - 1;
        }
        return columnName.toString();
    }
}
