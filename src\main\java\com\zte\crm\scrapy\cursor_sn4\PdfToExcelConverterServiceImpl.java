package com.zte.crm.scrapy.cursor_sn4;

import com.zte.crm.scrapy.cursor_sn4.IPdfToExcelConverterService;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;

/**
 * PDF转Excel转换服务实现类
 * 提供高质量的PDF到Excel转换服务
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-13
 */
@Service
public class PdfToExcelConverterServiceImpl implements IPdfToExcelConverterService {
    
    private static final Logger logger = LoggerFactory.getLogger(PdfToExcelConverterServiceImpl.class);
    
    private static final String SERVICE_VERSION = "2.0.0";
    private static final List<String> SUPPORTED_INPUT_FORMATS = Arrays.asList("pdf");
    private static final List<String> SUPPORTED_OUTPUT_FORMATS = Arrays.asList("xlsx", "xls");
    
    @Override
    public ConversionResult convertPdfToExcel(String pdfFilePath, String excelFilePath) throws IOException {
        return convertPdfToExcel(pdfFilePath, excelFilePath, ConversionConfig.getDefault());
    }
    
    @Override
    public ConversionResult convertPdfToExcel(String pdfFilePath, String excelFilePath, ConversionConfig config) throws IOException {
        File pdfFile = new File(pdfFilePath);
        File excelFile = new File(excelFilePath);
        return convertPdfToExcel(pdfFile, excelFile, config);
    }
    
    @Override
    public ConversionResult convertPdfToExcel(File pdfFile, File excelFile) throws IOException {
        return convertPdfToExcel(pdfFile, excelFile, ConversionConfig.getDefault());
    }
    
    @Override
    public ConversionResult convertPdfToExcel(File pdfFile, File excelFile, ConversionConfig config) throws IOException {
        long startTime = System.currentTimeMillis();
        ConversionResult result = new ConversionResult();
        
        try {
            logger.info("开始PDF转Excel转换: {} -> {}", pdfFile.getAbsolutePath(), excelFile.getAbsolutePath());
            
            // 验证输入文件
            if (!validatePdfFile(pdfFile)) {
                result.setSuccess(false);
                result.setMessage("无效的PDF文件");
                result.setErrorDetails("文件不存在、不可读或格式不正确");
                return result;
            }
            
            // 创建输出目录
            File parentDir = excelFile.getParentFile();
            if (parentDir != null && !parentDir.exists()) {
                parentDir.mkdirs();
            }
            
            // 分析PDF文件
            PdfFileInfo pdfInfo = analyzePdfFile(pdfFile);
            if (!pdfInfo.isValid()) {
                result.setSuccess(false);
                result.setMessage("PDF文件分析失败");
                result.setErrorDetails(pdfInfo.getErrorMessage());
                return result;
            }
            
            // 执行转换
            EnhancedPdfToExcelConverter.convertPdfToExcel(pdfFile, excelFile);
            
            // 记录转换结果
            long endTime = System.currentTimeMillis();
            long processingTime = endTime - startTime;
            
            result.setSuccess(true);
            result.setMessage("转换成功");
            result.setOutputFilePath(excelFile.getAbsolutePath());
            result.setProcessingTimeMs(processingTime);
            result.setPagesProcessed(pdfInfo.getPageCount());
            result.setInputFileSizeBytes(pdfFile.length());
            result.setOutputFileSizeBytes(excelFile.length());
            
            logger.info("PDF转Excel转换完成: {}", result.toString());
            
        } catch (Exception e) {
            logger.error("PDF转Excel转换失败", e);
            result.setSuccess(false);
            result.setMessage("转换过程中发生错误");
            result.setErrorDetails(e.getMessage());
            result.setProcessingTimeMs(System.currentTimeMillis() - startTime);
        }
        
        return result;
    }
    
    @Override
    public ConversionResult convertPdfToExcel(InputStream pdfInputStream, String excelFilePath) throws IOException {
        return convertPdfToExcel(pdfInputStream, excelFilePath, ConversionConfig.getDefault());
    }
    
    @Override
    public ConversionResult convertPdfToExcel(InputStream pdfInputStream, String excelFilePath, ConversionConfig config) throws IOException {
        // 创建临时PDF文件
        Path tempPdfPath = Files.createTempFile("temp_pdf_", ".pdf");
        File tempPdfFile = tempPdfPath.toFile();
        
        try {
            // 将输入流写入临时文件
            try (FileOutputStream fos = new FileOutputStream(tempPdfFile)) {
                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = pdfInputStream.read(buffer)) != -1) {
                    fos.write(buffer, 0, bytesRead);
                }
            }
            
            // 执行转换
            File excelFile = new File(excelFilePath);
            return convertPdfToExcel(tempPdfFile, excelFile, config);
            
        } finally {
            // 清理临时文件
            try {
                Files.deleteIfExists(tempPdfPath);
            } catch (IOException e) {
                logger.warn("删除临时文件失败: {}", tempPdfPath, e);
            }
        }
    }
    
    @Override
    public List<ConversionResult> batchConvertPdfToExcel(String inputDirectory, String outputDirectory) throws IOException {
        return batchConvertPdfToExcel(inputDirectory, outputDirectory, ConversionConfig.getDefault());
    }
    
    @Override
    public List<ConversionResult> batchConvertPdfToExcel(String inputDirectory, String outputDirectory, ConversionConfig config) throws IOException {
        List<ConversionResult> results = new ArrayList<>();
        
        File inputDir = new File(inputDirectory);
        File outputDir = new File(outputDirectory);
        
        if (!inputDir.exists() || !inputDir.isDirectory()) {
            ConversionResult errorResult = new ConversionResult(false, "输入目录不存在或不是目录: " + inputDirectory);
            results.add(errorResult);
            return results;
        }
        
        // 创建输出目录
        if (!outputDir.exists()) {
            outputDir.mkdirs();
        }
        
        // 查找所有PDF文件
        File[] pdfFiles = inputDir.listFiles((dir, name) -> 
            name.toLowerCase().endsWith(".pdf"));
        
        if (pdfFiles == null || pdfFiles.length == 0) {
            ConversionResult noFilesResult = new ConversionResult(false, "输入目录中没有找到PDF文件");
            results.add(noFilesResult);
            return results;
        }
        
        logger.info("开始批量转换，共找到 {} 个PDF文件", pdfFiles.length);
        
        // 批量转换
        for (File pdfFile : pdfFiles) {
            try {
                String fileName = pdfFile.getName();
                String excelFileName = fileName.substring(0, fileName.lastIndexOf('.')) + ".xlsx";
                File excelFile = new File(outputDir, excelFileName);
                
                ConversionResult result = convertPdfToExcel(pdfFile, excelFile, config);
                results.add(result);
                
                if (result.isSuccess()) {
                    logger.info("批量转换进度: {} -> {}", fileName, excelFileName);
                } else {
                    logger.warn("批量转换失败: {} - {}", fileName, result.getMessage());
                }
                
            } catch (Exception e) {
                logger.error("批量转换单个文件失败: {}", pdfFile.getName(), e);
                ConversionResult errorResult = new ConversionResult(false, "转换失败: " + e.getMessage());
                errorResult.setErrorDetails(e.getMessage());
                results.add(errorResult);
            }
        }
        
        long successCount = results.stream().mapToLong(r -> r.isSuccess() ? 1 : 0).sum();
        logger.info("批量转换完成，成功: {}/{}", successCount, results.size());
        
        return results;
    }
    
    @Override
    public boolean validatePdfFile(File pdfFile) {
        return EnhancedPdfToExcelConverter.validatePdfFile(pdfFile);
    }
    
    @Override
    public PdfFileInfo analyzePdfFile(File pdfFile) {
        PdfFileInfo info = new PdfFileInfo();
        
        try {
            if (!validatePdfFile(pdfFile)) {
                info.setValid(false);
                info.setErrorMessage("无效的PDF文件");
                return info;
            }
            
            info.setValid(true);
            info.setFileName(pdfFile.getName());
            info.setFileSizeBytes(pdfFile.length());
            
            // 分析PDF内容
            try (PDDocument document = PDDocument.load(pdfFile)) {
                info.setPageCount(document.getNumberOfPages());
                
                // 检查是否包含文本
                PDFTextStripper stripper = new PDFTextStripper();
                stripper.setStartPage(1);
                stripper.setEndPage(Math.min(3, document.getNumberOfPages())); // 只检查前3页
                String text = stripper.getText(document);
                info.setHasText(text != null && !text.trim().isEmpty());
                
                // 简单检测表格（基于文本格式）
                info.setHasTables(detectTables(text));
            }
            
        } catch (Exception e) {
            logger.error("分析PDF文件失败: {}", pdfFile.getAbsolutePath(), e);
            info.setValid(false);
            info.setErrorMessage("分析PDF文件时发生错误: " + e.getMessage());
        }
        
        return info;
    }
    
    @Override
    public List<String> getSupportedInputFormats() {
        return new ArrayList<>(SUPPORTED_INPUT_FORMATS);
    }
    
    @Override
    public List<String> getSupportedOutputFormats() {
        return new ArrayList<>(SUPPORTED_OUTPUT_FORMATS);
    }
    
    @Override
    public String getServiceVersion() {
        return SERVICE_VERSION;
    }
    
    /**
     * 简单检测是否包含表格结构
     * 
     * @param text 文本内容
     * @return 是否包含表格
     */
    private boolean detectTables(String text) {
        if (text == null || text.trim().isEmpty()) {
            return false;
        }
        
        String[] lines = text.split("\n");
        int tableRowCount = 0;
        
        for (String line : lines) {
            // 检查是否像表格行（包含多个空格分隔的列）
            String[] parts = line.trim().split("\\s{2,}");
            if (parts.length > 2) {
                tableRowCount++;
                if (tableRowCount >= 3) { // 至少3行像表格的内容
                    return true;
                }
            }
        }
        
        return false;
    }
    
    /**
     * 获取转换统计信息
     * 
     * @param results 转换结果列表
     * @return 统计信息字符串
     */
    public String getConversionStatistics(List<ConversionResult> results) {
        if (results == null || results.isEmpty()) {
            return "无转换记录";
        }
        
        long totalCount = results.size();
        long successCount = results.stream().mapToLong(r -> r.isSuccess() ? 1 : 0).sum();
        long failedCount = totalCount - successCount;
        long totalProcessingTime = results.stream().mapToLong(ConversionResult::getProcessingTimeMs).sum();
        long totalInputSize = results.stream().mapToLong(ConversionResult::getInputFileSizeBytes).sum();
        long totalOutputSize = results.stream().mapToLong(ConversionResult::getOutputFileSizeBytes).sum();
        
        return String.format(
            "转换统计：\n" +
            "总数: %d\n" +
            "成功: %d (%.1f%%)\n" +
            "失败: %d (%.1f%%)\n" +
            "总耗时: %.2f 秒\n" +
            "平均耗时: %.2f 秒/文件\n" +
            "输入总大小: %.2f MB\n" +
            "输出总大小: %.2f MB\n" +
            "压缩比: %.1f%%",
            totalCount,
            successCount, (successCount * 100.0 / totalCount),
            failedCount, (failedCount * 100.0 / totalCount),
            totalProcessingTime / 1000.0,
            (totalProcessingTime / 1000.0) / totalCount,
            totalInputSize / (1024.0 * 1024.0),
            totalOutputSize / (1024.0 * 1024.0),
            totalOutputSize > 0 ? (totalOutputSize * 100.0 / totalInputSize) : 0
        );
    }
}