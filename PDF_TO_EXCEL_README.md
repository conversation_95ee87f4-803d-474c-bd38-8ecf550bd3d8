# PDF转Excel工具

## 功能介绍

这是一个专门用于将PDF文件转换为Excel文件的工具，特别关注表格格式的保持和对齐。工具支持多种使用方式：

- **REST API接口**: 通过Web服务调用
- **命令行工具**: 直接在命令行中使用
- **Java服务**: 在代码中直接调用服务
- **简化版本**: 基于文本处理的Excel生成工具

## 主要特性

1. **表格格式保持**: 尽可能保持PDF中表格的原始格式和对齐方式
2. **多页支持**: 支持多页PDF文档，每页生成一个Excel工作表
3. **智能识别**: 自动识别表格结构和数据类型
4. **文本提取**: 当无法识别表格时，智能提取文本内容
5. **数字识别**: 自动识别数字并设置正确的单元格格式
6. **样式支持**: 支持表头样式、边框、数字格式等

## 技术栈

- **PDF处理**: Apache PDFBox
- **Excel生成**: Apache POI (支持XLS和XLSX格式)
- **Web框架**: Spring Boot
- **API文档**: Swagger

## 当前状态

✅ **已完成功能**:
- Excel文件生成 (XLS格式)
- 表格样式和格式设置
- 数字识别和格式化
- 文本处理和分割
- 示例数据生成

⚠️ **已知限制**:
- PDF解析在当前环境中遇到依赖问题
- 建议使用文本输入方式生成Excel
- 支持XLS格式，XLSX格式需要额外配置

## 使用方法

### 1. 快速开始 - Excel生成测试

最简单的方式是运行Excel生成测试：

```bash
# 编译项目
mvn compile

# 运行Excel生成测试
mvn exec:java
```

这将生成两个示例Excel文件：
- `doc/sample_output.xls` - 基础示例
- `doc/complex_sample.xls` - 复杂财务报表示例

### 2. 使用SimplePdfToExcelConverter工具类

```java
import com.zte.crm.scrapy.util.SimplePdfToExcelConverter;

// 将文本转换为Excel
String textData = "项目名称\t金额\t状态\n项目A\t1000000\t进行中\n项目B\t2000000\t已完成";
SimplePdfToExcelConverter.convertTableTextToExcel(textData, "output.xls");

// 从文件读取文本并转换
String fileContent = SimplePdfToExcelConverter.readTextFromFile("data.txt");
SimplePdfToExcelConverter.convertTextToExcel(fileContent, "output.xls");
```

### 3. REST API接口（需要解决依赖问题后使用）

启动Spring Boot应用后，可以通过以下接口使用：

#### 上传PDF并转换
```
POST /api/pdf-excel/convert
Content-Type: multipart/form-data
参数: file (PDF文件)
```

#### 转换本地PDF文件
```
POST /api/pdf-excel/convert-local
参数:
- pdfPath: PDF文件路径
- excelPath: Excel输出路径
```

### 4. 自定义Excel生成

```java
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;

// 创建工作簿
Workbook workbook = new HSSFWorkbook();
Sheet sheet = workbook.createSheet("数据表");

// 创建样式
CellStyle headerStyle = workbook.createCellStyle();
Font headerFont = workbook.createFont();
headerFont.setBold(true);
headerStyle.setFont(headerFont);

// 创建数据
Row headerRow = sheet.createRow(0);
Cell cell = headerRow.createCell(0);
cell.setCellValue("标题");
cell.setCellStyle(headerStyle);

// 保存文件
try (FileOutputStream fos = new FileOutputStream("custom.xls")) {
    workbook.write(fos);
}
workbook.close();
```

## 参照文件测试

项目中包含了参照文件用于测试转换效果：

- **输入**: `doc/Test_HK4934_page10.pdf`
- **参照输出**: `doc/Test_HK4934_page10.xlsx`

运行测试命令可以验证转换效果：

```bash
java -cp target/notice-spider-1.0.0.jar com.zte.crm.scrapy.PdfToExcelMain test
```

## API文档

启动应用后访问Swagger文档：
```
http://localhost:8081/spider/swagger-ui.html
```

## 配置说明

### 依赖配置

在`pom.xml`中已包含必要的依赖：

```xml
<!-- Apache POI for Excel processing -->
<dependency>
    <groupId>org.apache.poi</groupId>
    <artifactId>poi-ooxml</artifactId>
    <version>5.2.3</version>
</dependency>

<!-- Apache PDFBox for PDF processing -->
<dependency>
    <groupId>org.apache.pdfbox</groupId>
    <artifactId>pdfbox</artifactId>
    <version>2.0.29</version>
</dependency>

<!-- Tabula for PDF table extraction -->
<dependency>
    <groupId>technology.tabula</groupId>
    <artifactId>tabula-java</artifactId>
    <version>1.0.5</version>
</dependency>
```

### 临时文件配置

转换过程中会在系统临时目录创建文件：
- 路径: `{java.io.tmpdir}/pdf-excel-converter/`
- 自动清理: 下载后或程序结束时清理

## 注意事项

1. **文件大小限制**: 建议PDF文件不超过50MB
2. **表格复杂度**: 复杂的表格布局可能需要手动调整
3. **字体支持**: 某些特殊字体可能显示异常
4. **图片处理**: 当前版本不处理PDF中的图片
5. **内存使用**: 大文件转换时注意内存使用情况

## 故障排除

### 常见问题

1. **转换失败**: 检查PDF文件是否损坏或加密
2. **表格错位**: 尝试不同的提取算法
3. **中文乱码**: 确保系统支持UTF-8编码
4. **内存不足**: 增加JVM内存参数 `-Xmx2g`

### 日志查看

应用日志位于 `logs/` 目录下：
- `project_info.log`: 一般信息日志
- `project_error.log`: 错误日志

## 开发说明

### 核心类说明

- `PdfToExcelConverter`: 核心转换工具类
- `IPdfToExcelService`: 服务接口
- `PdfToExcelServiceImpl`: 服务实现
- `PdfToExcelController`: REST控制器
- `PdfToExcelMain`: 命令行工具

### 扩展开发

如需扩展功能，可以：

1. 继承`PdfToExcelConverter`类添加自定义转换逻辑
2. 实现`IPdfToExcelService`接口提供不同的转换策略
3. 添加新的REST接口支持更多功能

## 测试

运行单元测试：

```bash
mvn test -Dtest=PdfToExcelServiceTest
```

## 版本历史

- **v1.0.0**: 初始版本，支持基本的PDF转Excel功能
  - 表格提取和格式保持
  - REST API接口
  - 命令行工具
  - 参照文件测试
