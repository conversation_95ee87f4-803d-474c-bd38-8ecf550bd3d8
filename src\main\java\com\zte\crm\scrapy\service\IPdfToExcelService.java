package com.zte.crm.scrapy.service;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;

/**
 * PDF转Excel服务接口
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-13
 */
public interface IPdfToExcelService {
    
    /**
     * 将PDF文件转换为Excel文件
     * 
     * @param pdfFilePath PDF文件路径
     * @param excelFilePath Excel输出文件路径
     * @return 转换结果信息
     * @throws IOException 文件操作异常
     */
    String convertPdfToExcel(String pdfFilePath, String excelFilePath) throws IOException;
    
    /**
     * 将PDF文件转换为Excel文件
     * 
     * @param pdfFile PDF文件
     * @param excelFile Excel输出文件
     * @return 转换结果信息
     * @throws IOException 文件操作异常
     */
    String convertPdfToExcel(File pdfFile, File excelFile) throws IOException;
    
    /**
     * 将PDF输入流转换为Excel文件
     * 
     * @param pdfInputStream PDF输入流
     * @param excelFilePath Excel输出文件路径
     * @return 转换结果信息
     * @throws IOException 文件操作异常
     */
    String convertPdfToExcel(InputStream pdfInputStream, String excelFilePath) throws IOException;
    
    /**
     * 验证PDF文件是否有效
     * 
     * @param pdfFile PDF文件
     * @return 是否有效
     */
    boolean validatePdfFile(File pdfFile);
    
    /**
     * 获取PDF文件的页数
     * 
     * @param pdfFile PDF文件
     * @return 页数
     * @throws IOException 文件操作异常
     */
    int getPdfPageCount(File pdfFile) throws IOException;
}
