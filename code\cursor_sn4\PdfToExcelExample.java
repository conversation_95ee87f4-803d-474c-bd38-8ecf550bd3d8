package com.alix.pdf2excel.example;

import com.alix.pdf2excel.service.IPdfToExcelConverterService;
import com.alix.pdf2excel.service.impl.PdfToExcelConverterServiceImpl;
import com.alix.pdf2excel.util.EnhancedPdfToExcelConverter;

import java.io.File;
import java.io.IOException;
import java.util.List;

/**
 * PDF转Excel使用示例
 * 展示如何使用工具类和服务接口
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-13
 */
public class PdfToExcelExample {
    
    public static void main(String[] args) {
        System.out.println("=== PDF转Excel工具使用示例 ===\n");
        
        // 示例文件路径
        String inputPdfPath = "doc/Test_HK4934_page10.pdf";
        String outputDir = "code/cursor_sn4/output/";
        
        // 创建输出目录
        new File(outputDir).mkdirs();
        
        // 示例1：直接使用工具类
        example1_DirectToolUsage(inputPdfPath, outputDir);
        
        // 示例2：使用服务接口（默认配置）
        example2_ServiceWithDefaultConfig(inputPdfPath, outputDir);
        
        // 示例3：使用高质量配置
        example3_HighQualityConversion(inputPdfPath, outputDir);
        
        // 示例4：使用快速配置
        example4_FastConversion(inputPdfPath, outputDir);
        
        // 示例5：批量转换
        example5_BatchConversion();
        
        // 示例6：PDF文件分析
        example6_PdfAnalysis(inputPdfPath);
        
        // 示例7：自定义配置
        example7_CustomConfiguration(inputPdfPath, outputDir);
        
        System.out.println("\n=== 所有示例执行完成 ===");
    }
    
    /**
     * 示例1：直接使用增强工具类
     */
    public static void example1_DirectToolUsage(String inputPath, String outputDir) {
        System.out.println("--- 示例1：直接使用工具类 ---");
        
        try {
            File inputFile = new File(inputPath);
            if (!inputFile.exists()) {
                System.out.println("输入文件不存在，跳过此示例: " + inputPath);
                return;
            }
            
            File outputFile = new File(outputDir, "example1_direct_tool.xlsx");
            
            System.out.println("输入文件: " + inputFile.getAbsolutePath());
            System.out.println("输出文件: " + outputFile.getAbsolutePath());
            
            long startTime = System.currentTimeMillis();
            EnhancedPdfToExcelConverter.convertPdfToExcel(inputFile, outputFile);
            long endTime = System.currentTimeMillis();
            
            System.out.println("转换完成！");
            System.out.println("耗时: " + (endTime - startTime) + " ms");
            System.out.println("输出文件大小: " + String.format("%.2f KB", outputFile.length() / 1024.0));
            
        } catch (IOException e) {
            System.err.println("转换失败: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    /**
     * 示例2：使用服务接口（默认配置）
     */
    public static void example2_ServiceWithDefaultConfig(String inputPath, String outputDir) {
        System.out.println("--- 示例2：使用服务接口（默认配置） ---");
        
        try {
            File inputFile = new File(inputPath);
            if (!inputFile.exists()) {
                System.out.println("输入文件不存在，跳过此示例: " + inputPath);
                return;
            }
            
            IPdfToExcelConverterService service = new PdfToExcelConverterServiceImpl();
            File outputFile = new File(outputDir, "example2_service_default.xlsx");
            
            IPdfToExcelConverterService.ConversionResult result = 
                service.convertPdfToExcel(inputFile, outputFile);
            
            System.out.println("转换结果: " + result.toString());
            
        } catch (IOException e) {
            System.err.println("转换失败: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    /**
     * 示例3：使用高质量配置
     */
    public static void example3_HighQualityConversion(String inputPath, String outputDir) {
        System.out.println("--- 示例3：高质量转换配置 ---");
        
        try {
            File inputFile = new File(inputPath);
            if (!inputFile.exists()) {
                System.out.println("输入文件不存在，跳过此示例: " + inputPath);
                return;
            }
            
            IPdfToExcelConverterService service = new PdfToExcelConverterServiceImpl();
            File outputFile = new File(outputDir, "example3_high_quality.xlsx");
            
            IPdfToExcelConverterService.ConversionConfig config = 
                IPdfToExcelConverterService.ConversionConfig.getHighQuality();
            
            System.out.println("使用高质量配置:");
            System.out.println("- 保持表格对齐: " + config.isPreserveTableAlignment());
            System.out.println("- 自动检测数字: " + config.isAutoDetectNumbers());
            System.out.println("- 合并单元格: " + config.isMergeCells());
            System.out.println("- 应用样式: " + config.isApplyStyles());
            
            IPdfToExcelConverterService.ConversionResult result = 
                service.convertPdfToExcel(inputFile, outputFile, config);
            
            System.out.println("转换结果: " + result.toString());
            
        } catch (IOException e) {
            System.err.println("转换失败: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    /**
     * 示例4：使用快速配置
     */
    public static void example4_FastConversion(String inputPath, String outputDir) {
        System.out.println("--- 示例4：快速转换配置 ---");
        
        try {
            File inputFile = new File(inputPath);
            if (!inputFile.exists()) {
                System.out.println("输入文件不存在，跳过此示例: " + inputPath);
                return;
            }
            
            IPdfToExcelConverterService service = new PdfToExcelConverterServiceImpl();
            File outputFile = new File(outputDir, "example4_fast.xlsx");
            
            IPdfToExcelConverterService.ConversionConfig config = 
                IPdfToExcelConverterService.ConversionConfig.getFast();
            
            System.out.println("使用快速配置（牺牲质量换取速度）:");
            System.out.println("- 保持表格对齐: " + config.isPreserveTableAlignment());
            System.out.println("- 自动检测数字: " + config.isAutoDetectNumbers());
            System.out.println("- 合并单元格: " + config.isMergeCells());
            System.out.println("- 应用样式: " + config.isApplyStyles());
            
            IPdfToExcelConverterService.ConversionResult result = 
                service.convertPdfToExcel(inputFile, outputFile, config);
            
            System.out.println("转换结果: " + result.toString());
            
        } catch (IOException e) {
            System.err.println("转换失败: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    /**
     * 示例5：批量转换
     */
    public static void example5_BatchConversion() {
        System.out.println("--- 示例5：批量转换 ---");
        
        try {
            String inputDir = "doc/";  // 假设有多个PDF文件的目录
            String outputDir = "code/cursor_sn4/output/batch/";
            
            // 创建输出目录
            new File(outputDir).mkdirs();
            
            IPdfToExcelConverterService service = new PdfToExcelConverterServiceImpl();
            
            List<IPdfToExcelConverterService.ConversionResult> results = 
                service.batchConvertPdfToExcel(inputDir, outputDir);
            
            System.out.println("批量转换完成，处理了 " + results.size() + " 个文件:");
            
            int successCount = 0;
            for (IPdfToExcelConverterService.ConversionResult result : results) {
                if (result.isSuccess()) {
                    successCount++;
                    System.out.println("✓ " + result.getMessage());
                } else {
                    System.out.println("✗ " + result.getMessage());
                }
            }
            
            System.out.println("成功转换: " + successCount + "/" + results.size());
            
            // 获取详细统计信息
            if (service instanceof PdfToExcelConverterServiceImpl) {
                PdfToExcelConverterServiceImpl impl = (PdfToExcelConverterServiceImpl) service;
                String statistics = impl.getConversionStatistics(results);
                System.out.println("\n统计信息:");
                System.out.println(statistics);
            }
            
        } catch (IOException e) {
            System.err.println("批量转换失败: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    /**
     * 示例6：PDF文件分析
     */
    public static void example6_PdfAnalysis(String inputPath) {
        System.out.println("--- 示例6：PDF文件分析 ---");
        
        File inputFile = new File(inputPath);
        if (!inputFile.exists()) {
            System.out.println("输入文件不存在，跳过此示例: " + inputPath);
            return;
        }
        
        IPdfToExcelConverterService service = new PdfToExcelConverterServiceImpl();
        
        // 基本验证
        boolean isValid = service.validatePdfFile(inputFile);
        System.out.println("PDF文件验证: " + (isValid ? "有效" : "无效"));
        
        if (isValid) {
            // 详细分析
            IPdfToExcelConverterService.PdfFileInfo info = service.analyzePdfFile(inputFile);
            System.out.println("详细分析结果:");
            System.out.println(info.toString());
            
            // 推荐配置
            if (info.isHasTables()) {
                System.out.println("推荐配置: 高质量配置（检测到表格）");
            } else if (info.isHasText()) {
                System.out.println("推荐配置: 默认配置（主要是文本内容）");
            } else {
                System.out.println("推荐配置: 快速配置（可能主要是图像内容）");
            }
        }
        
        System.out.println();
    }
    
    /**
     * 示例7：自定义配置
     */
    public static void example7_CustomConfiguration(String inputPath, String outputDir) {
        System.out.println("--- 示例7：自定义配置 ---");
        
        try {
            File inputFile = new File(inputPath);
            if (!inputFile.exists()) {
                System.out.println("输入文件不存在，跳过此示例: " + inputPath);
                return;
            }
            
            IPdfToExcelConverterService service = new PdfToExcelConverterServiceImpl();
            File outputFile = new File(outputDir, "example7_custom.xlsx");
            
            // 创建自定义配置
            IPdfToExcelConverterService.ConversionConfig config = 
                new IPdfToExcelConverterService.ConversionConfig();
            
            // 自定义设置
            config.setPreserveTableAlignment(true);      // 保持表格对齐
            config.setAutoDetectNumbers(true);           // 自动检测数字
            config.setMergeCells(false);                 // 不合并单元格
            config.setApplyStyles(true);                 // 应用样式
            config.setAutoSizeColumns(true);             // 自动调整列宽
            config.setMaxColumnWidth(20000);             // 最大列宽
            config.setCreateSeparateSheets(true);        // 为每页创建工作表
            config.setSheetNamePrefix("页面_");           // 工作表名前缀
            config.setIncludeEmptyRows(false);           // 不包含空行
            config.setDetectHeaders(true);               // 检测标题行
            
            System.out.println("使用自定义配置:");
            System.out.println("- 保持表格对齐: " + config.isPreserveTableAlignment());
            System.out.println("- 自动检测数字: " + config.isAutoDetectNumbers());
            System.out.println("- 合并单元格: " + config.isMergeCells());
            System.out.println("- 应用样式: " + config.isApplyStyles());
            System.out.println("- 最大列宽: " + config.getMaxColumnWidth());
            System.out.println("- 工作表前缀: " + config.getSheetNamePrefix());
            
            IPdfToExcelConverterService.ConversionResult result = 
                service.convertPdfToExcel(inputFile, outputFile, config);
            
            System.out.println("转换结果: " + result.toString());
            
        } catch (IOException e) {
            System.err.println("转换失败: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    /**
     * 展示服务信息
     */
    public static void showServiceInfo() {
        System.out.println("--- 服务信息 ---");
        
        IPdfToExcelConverterService service = new PdfToExcelConverterServiceImpl();
        
        System.out.println("服务版本: " + service.getServiceVersion());
        System.out.println("支持的输入格式: " + service.getSupportedInputFormats());
        System.out.println("支持的输出格式: " + service.getSupportedOutputFormats());
        
        System.out.println();
    }
    
    /**
     * 性能测试比较
     */
    public static void performanceComparison(String inputPath, String outputDir) {
        System.out.println("--- 性能比较测试 ---");
        
        File inputFile = new File(inputPath);
        if (!inputFile.exists()) {
            System.out.println("输入文件不存在，跳过性能测试: " + inputPath);
            return;
        }
        
        IPdfToExcelConverterService service = new PdfToExcelConverterServiceImpl();
        
        try {
            // 测试不同配置的性能
            System.out.println("测试文件: " + inputFile.getName() + 
                " (大小: " + String.format("%.2f KB", inputFile.length() / 1024.0) + ")");
            
            // 快速配置
            File fastOutput = new File(outputDir, "perf_fast.xlsx");
            long startTime = System.currentTimeMillis();
            IPdfToExcelConverterService.ConversionResult fastResult = 
                service.convertPdfToExcel(inputFile, fastOutput, 
                    IPdfToExcelConverterService.ConversionConfig.getFast());
            long fastTime = System.currentTimeMillis() - startTime;
            
            // 默认配置
            File defaultOutput = new File(outputDir, "perf_default.xlsx");
            startTime = System.currentTimeMillis();
            IPdfToExcelConverterService.ConversionResult defaultResult = 
                service.convertPdfToExcel(inputFile, defaultOutput);
            long defaultTime = System.currentTimeMillis() - startTime;
            
            // 高质量配置
            File hqOutput = new File(outputDir, "perf_high_quality.xlsx");
            startTime = System.currentTimeMillis();
            IPdfToExcelConverterService.ConversionResult hqResult = 
                service.convertPdfToExcel(inputFile, hqOutput, 
                    IPdfToExcelConverterService.ConversionConfig.getHighQuality());
            long hqTime = System.currentTimeMillis() - startTime;
            
            // 输出比较结果
            System.out.println("\n性能比较结果:");
            System.out.printf("%-12s | %-8s | %-10s | %-8s\n", 
                "配置", "耗时(ms)", "输出大小(KB)", "状态");
            System.out.println("-------------|----------|------------|--------");
            System.out.printf("%-12s | %-8d | %-10.2f | %-8s\n", 
                "快速", fastTime, fastOutput.length() / 1024.0, 
                fastResult.isSuccess() ? "成功" : "失败");
            System.out.printf("%-12s | %-8d | %-10.2f | %-8s\n", 
                "默认", defaultTime, defaultOutput.length() / 1024.0, 
                defaultResult.isSuccess() ? "成功" : "失败");
            System.out.printf("%-12s | %-8d | %-10.2f | %-8s\n", 
                "高质量", hqTime, hqOutput.length() / 1024.0, 
                hqResult.isSuccess() ? "成功" : "失败");
            
        } catch (IOException e) {
            System.err.println("性能测试失败: " + e.getMessage());
        }
        
        System.out.println();
    }
}
