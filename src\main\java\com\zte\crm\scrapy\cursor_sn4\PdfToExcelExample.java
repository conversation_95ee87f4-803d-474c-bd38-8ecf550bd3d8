package com.zte.crm.scrapy.cursor_sn4;

import java.io.File;
import java.io.IOException;

/**
 * PDF转Excel使用示例
 * 展示如何使用工具类和服务接口
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-13
 */
public class PdfToExcelExample {
    
    public static void main(String[] args) {
        System.out.println("=== PDF转Excel工具使用示例 ===\n");
        
        // 示例文件路径
        String inputPdfPath = "doc/Test_HK4934_page10.pdf";
        String outputDir = "src/main/java/com/zte/crm/scrapy/cursor_sn4/output/";
        
        // 创建输出目录
        new File(outputDir).mkdirs();
        
        // 示例1：直接使用工具类
        example1_DirectToolUsage(inputPdfPath, outputDir);
        
        // 示例2：使用服务接口（默认配置）
        example2_ServiceWithDefaultConfig(inputPdfPath, outputDir);
        
        // 示例3：使用高质量配置
        example3_HighQualityConversion(inputPdfPath, outputDir);
        
        // 示例4：使用快速配置
        example4_FastConversion(inputPdfPath, outputDir);
        
        // 示例5：PDF文件分析
        example5_PdfAnalysis(inputPdfPath);
        
        // 示例6：自定义配置
        example6_CustomConfiguration(inputPdfPath, outputDir);
        
        System.out.println("\n=== 所有示例执行完成 ===");
    }
    
    /**
     * 示例1：直接使用增强工具类
     */
    public static void example1_DirectToolUsage(String inputPath, String outputDir) {
        System.out.println("--- 示例1：直接使用工具类 ---");
        
        try {
            File inputFile = new File(inputPath);
            if (!inputFile.exists()) {
                System.out.println("输入文件不存在，跳过此示例: " + inputPath);
                return;
            }
            
            File outputFile = new File(outputDir, "example1_direct_tool.xlsx");
            
            System.out.println("输入文件: " + inputFile.getAbsolutePath());
            System.out.println("输出文件: " + outputFile.getAbsolutePath());
            
            long startTime = System.currentTimeMillis();
            EnhancedPdfToExcelConverter.convertPdfToExcel(inputFile, outputFile);
            long endTime = System.currentTimeMillis();
            
            System.out.println("转换完成！");
            System.out.println("耗时: " + (endTime - startTime) + " ms");
            System.out.println("输出文件大小: " + String.format("%.2f KB", outputFile.length() / 1024.0));
            
        } catch (IOException e) {
            System.err.println("转换失败: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    /**
     * 示例2：使用服务接口（默认配置）
     */
    public static void example2_ServiceWithDefaultConfig(String inputPath, String outputDir) {
        System.out.println("--- 示例2：使用服务接口（默认配置） ---");
        
        try {
            File inputFile = new File(inputPath);
            if (!inputFile.exists()) {
                System.out.println("输入文件不存在，跳过此示例: " + inputPath);
                return;
            }
            
            IPdfToExcelConverterService service = new PdfToExcelConverterServiceImpl();
            File outputFile = new File(outputDir, "example2_service_default.xlsx");
            
            IPdfToExcelConverterService.ConversionResult result = 
                service.convertPdfToExcel(inputFile, outputFile);
            
            System.out.println("转换结果: " + result.toString());
            
        } catch (IOException e) {
            System.err.println("转换失败: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    /**
     * 示例3：使用高质量配置
     */
    public static void example3_HighQualityConversion(String inputPath, String outputDir) {
        System.out.println("--- 示例3：高质量转换配置 ---");
        
        try {
            File inputFile = new File(inputPath);
            if (!inputFile.exists()) {
                System.out.println("输入文件不存在，跳过此示例: " + inputPath);
                return;
            }
            
            IPdfToExcelConverterService service = new PdfToExcelConverterServiceImpl();
            File outputFile = new File(outputDir, "example3_high_quality.xlsx");
            
            IPdfToExcelConverterService.ConversionConfig config = 
                IPdfToExcelConverterService.ConversionConfig.getHighQuality();
            
            System.out.println("使用高质量配置:");
            System.out.println("- 保持表格对齐: " + config.isPreserveTableAlignment());
            System.out.println("- 自动检测数字: " + config.isAutoDetectNumbers());
            System.out.println("- 合并单元格: " + config.isMergeCells());
            System.out.println("- 应用样式: " + config.isApplyStyles());
            
            IPdfToExcelConverterService.ConversionResult result = 
                service.convertPdfToExcel(inputFile, outputFile, config);
            
            System.out.println("转换结果: " + result.toString());
            
        } catch (IOException e) {
            System.err.println("转换失败: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    /**
     * 示例4：使用快速配置
     */
    public static void example4_FastConversion(String inputPath, String outputDir) {
        System.out.println("--- 示例4：快速转换配置 ---");
        
        try {
            File inputFile = new File(inputPath);
            if (!inputFile.exists()) {
                System.out.println("输入文件不存在，跳过此示例: " + inputPath);
                return;
            }
            
            IPdfToExcelConverterService service = new PdfToExcelConverterServiceImpl();
            File outputFile = new File(outputDir, "example4_fast.xlsx");
            
            IPdfToExcelConverterService.ConversionConfig config = 
                IPdfToExcelConverterService.ConversionConfig.getFast();
            
            System.out.println("使用快速配置（牺牲质量换取速度）:");
            System.out.println("- 保持表格对齐: " + config.isPreserveTableAlignment());
            System.out.println("- 自动检测数字: " + config.isAutoDetectNumbers());
            System.out.println("- 合并单元格: " + config.isMergeCells());
            System.out.println("- 应用样式: " + config.isApplyStyles());
            
            IPdfToExcelConverterService.ConversionResult result = 
                service.convertPdfToExcel(inputFile, outputFile, config);
            
            System.out.println("转换结果: " + result.toString());
            
        } catch (IOException e) {
            System.err.println("转换失败: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    /**
     * 示例5：PDF文件分析
     */
    public static void example5_PdfAnalysis(String inputPath) {
        System.out.println("--- 示例5：PDF文件分析 ---");
        
        File inputFile = new File(inputPath);
        if (!inputFile.exists()) {
            System.out.println("输入文件不存在，跳过此示例: " + inputPath);
            return;
        }
        
        IPdfToExcelConverterService service = new PdfToExcelConverterServiceImpl();
        
        // 基本验证
        boolean isValid = service.validatePdfFile(inputFile);
        System.out.println("PDF文件验证: " + (isValid ? "有效" : "无效"));
        
        if (isValid) {
            // 详细分析
            IPdfToExcelConverterService.PdfFileInfo info = service.analyzePdfFile(inputFile);
            System.out.println("详细分析结果:");
            System.out.println(info.toString());
            
            // 推荐配置
            if (info.isHasTables()) {
                System.out.println("推荐配置: 高质量配置（检测到表格）");
            } else if (info.isHasText()) {
                System.out.println("推荐配置: 默认配置（主要是文本内容）");
            } else {
                System.out.println("推荐配置: 快速配置（可能主要是图像内容）");
            }
        }
        
        System.out.println();
    }
    
    /**
     * 示例6：自定义配置
     */
    public static void example6_CustomConfiguration(String inputPath, String outputDir) {
        System.out.println("--- 示例6：自定义配置 ---");
        
        try {
            File inputFile = new File(inputPath);
            if (!inputFile.exists()) {
                System.out.println("输入文件不存在，跳过此示例: " + inputPath);
                return;
            }
            
            IPdfToExcelConverterService service = new PdfToExcelConverterServiceImpl();
            File outputFile = new File(outputDir, "example6_custom.xlsx");
            
            // 创建自定义配置
            IPdfToExcelConverterService.ConversionConfig config = 
                new IPdfToExcelConverterService.ConversionConfig();
            
            // 自定义设置
            config.setPreserveTableAlignment(true);      // 保持表格对齐
            config.setAutoDetectNumbers(true);           // 自动检测数字
            config.setMergeCells(false);                 // 不合并单元格
            config.setApplyStyles(true);                 // 应用样式
            config.setAutoSizeColumns(true);             // 自动调整列宽
            config.setMaxColumnWidth(20000);             // 最大列宽
            config.setCreateSeparateSheets(true);        // 为每页创建工作表
            config.setSheetNamePrefix("页面_");           // 工作表名前缀
            config.setIncludeEmptyRows(false);           // 不包含空行
            config.setDetectHeaders(true);               // 检测标题行
            
            System.out.println("使用自定义配置:");
            System.out.println("- 保持表格对齐: " + config.isPreserveTableAlignment());
            System.out.println("- 自动检测数字: " + config.isAutoDetectNumbers());
            System.out.println("- 合并单元格: " + config.isMergeCells());
            System.out.println("- 应用样式: " + config.isApplyStyles());
            System.out.println("- 最大列宽: " + config.getMaxColumnWidth());
            System.out.println("- 工作表前缀: " + config.getSheetNamePrefix());
            
            IPdfToExcelConverterService.ConversionResult result = 
                service.convertPdfToExcel(inputFile, outputFile, config);
            
            System.out.println("转换结果: " + result.toString());
            
        } catch (IOException e) {
            System.err.println("转换失败: " + e.getMessage());
        }
        
        System.out.println();
    }
}
