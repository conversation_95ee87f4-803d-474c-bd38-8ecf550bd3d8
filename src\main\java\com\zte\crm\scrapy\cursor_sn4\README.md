# PDF转Excel工具类

## 📁 文件说明

在 `src/main/java/com/zte/crm/scrapy/cursor_sn4/` 目录下包含以下文件：

### 核心文件
1. **`EnhancedPdfToExcelConverter.java`** - 增强版PDF转Excel工具类
   - 智能表格检测和文本分析
   - 格式保持和对齐优化  
   - 支持数据类型自动识别（数字、货币、日期）
   - 多种样式支持（标题、数据、数字）

2. **`IPdfToExcelConverterService.java`** - 服务接口
   - 完整的服务接口定义
   - 配置类和结果类
   - 支持多种转换模式（高质量、快速、默认）

3. **`PdfToExcelConverterServiceImpl.java`** - 服务实现类  
   - 完整的服务实现
   - 错误处理和日志记录
   - 批量转换功能

4. **`PdfToExcelExample.java`** - 使用示例
   - 多个详细的使用示例
   - 不同配置的对比演示

5. **`SimpleTest.java`** - 简单测试程序
   - 基本功能验证
   - 快速测试转换效果

## 🎯 核心特性

### 表格对齐和格式保持
- ✅ 使用PDFBox进行文本提取和分析
- ✅ 智能识别表格结构和对齐
- ✅ 保持原PDF中的数据格式
- ✅ 自动列宽调整

### 多种转换模式
- ✅ **高质量模式**：最大程度保持原格式
- ✅ **快速模式**：优化处理速度
- ✅ **默认模式**：平衡质量和速度

### 数据类型识别
- ✅ 自动识别数字、货币、日期等
- ✅ 应用相应的Excel格式
- ✅ 支持千分位分隔符

### 样式和美化
- ✅ 标题行自动加粗和背景色
- ✅ 表格边框和分隔线
- ✅ 数字右对齐，文本左对齐

## 🚀 使用方法

### 基本使用

```java
// 方法1：直接使用工具类
File pdfFile = new File("input.pdf");
File excelFile = new File("output.xlsx");
EnhancedPdfToExcelConverter.convertPdfToExcel(pdfFile, excelFile);

// 方法2：使用服务接口
IPdfToExcelConverterService service = new PdfToExcelConverterServiceImpl();
ConversionResult result = service.convertPdfToExcel("input.pdf", "output.xlsx");
```

### 高质量转换（推荐）

```java
IPdfToExcelConverterService service = new PdfToExcelConverterServiceImpl();
ConversionConfig config = ConversionConfig.getHighQuality();
ConversionResult result = service.convertPdfToExcel(
    "doc/Test_HK4934_page10.pdf", 
    "output/Test_HK4934_page10.xlsx", 
    config
);
System.out.println(result.toString());
```

### 批量转换

```java
List<ConversionResult> results = service.batchConvertPdfToExcel(
    "input_directory/", 
    "output_directory/"
);
```

## 🔧 配置选项

| 配置项 | 说明 | 默认值 |
|--------|------|---------|
| `preserveTableAlignment` | 保持表格对齐 | true |
| `autoDetectNumbers` | 自动检测数字类型 | true |
| `mergeCells` | 支持合并单元格 | true |
| `applyStyles` | 应用格式样式 | true |
| `autoSizeColumns` | 自动调整列宽 | true |
| `maxColumnWidth` | 最大列宽限制 | 15000 |
| `createSeparateSheets` | 为每页创建工作表 | true |
| `sheetNamePrefix` | 工作表名前缀 | "Page " |
| `includeEmptyRows` | 包含空行 | false |
| `detectHeaders` | 检测标题行 | true |

## 📦 依赖要求

确保项目包含以下依赖：

```xml
<!-- Apache POI for Excel processing -->
<dependency>
    <groupId>org.apache.poi</groupId>
    <artifactId>poi-ooxml</artifactId>
    <version>5.2.3</version>
</dependency>

<!-- Apache PDFBox for PDF processing -->
<dependency>
    <groupId>org.apache.pdfbox</groupId>
    <artifactId>pdfbox</artifactId>
    <version>2.0.29</version>
</dependency>

<!-- Spring Boot for service -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter</artifactId>
</dependency>
```

## 🧪 运行测试

### 编译项目
```bash
mvn clean compile
```

### 运行简单测试
```bash
java -cp "target/classes;your-dependencies" com.zte.crm.scrapy.cursor_sn4.SimpleTest
```

### 运行示例程序
```bash
java -cp "target/classes;your-dependencies" com.zte.crm.scrapy.cursor_sn4.PdfToExcelExample
```

## 📋 转换质量优化建议

1. **表格对齐问题**：
   - 确保PDF中的表格有清晰的结构
   - 避免使用只有空格分隔的伪表格
   - 复杂表格建议使用高质量配置

2. **数据类型识别**：
   - 数字格式应保持一致
   - 日期格式建议使用标准格式
   - 货币符号应明确标识

3. **性能优化**：
   - 大文件建议分页处理
   - 批量转换时考虑使用快速配置
   - 合理设置最大列宽避免过宽

## ❓ 常见问题

### Q: 编译报错怎么办？
A: 
1. 确保所有依赖都已正确添加到pom.xml
2. 运行 `mvn clean compile` 重新编译
3. 检查Java版本是否为1.8+

### Q: 转换后的表格对齐不正确？
A: 
- 检查原PDF是否为真正的表格结构
- 尝试使用高质量配置
- 确保PDF文件质量良好

### Q: 数字没有被正确识别？
A: 
- 检查数字格式是否标准
- 确保启用了`autoDetectNumbers`配置
- 可能需要手动后处理特殊格式

## 📝 版本信息

- 当前版本：2.0.0
- 支持的输入格式：PDF
- 支持的输出格式：XLSX
- Java版本要求：1.8+
- 依赖库：PDFBox 2.0.29, POI 5.2.3

## 📚 参考文件

本工具的开发参考了以下文件的转换效果：
- `doc/Test_HK4934_page10.pdf` - 原始PDF文件
- `doc/Test_HK4934_page10.xlsx` - 参考转换结果

可以使用这些文件来验证和比较转换质量。

## 🎉 成功解决编译问题

✅ **编译状态：成功**  
✅ **依赖解决：已移除不可用的Tabula依赖**  
✅ **代码重构：使用纯PDFBox方案**  
✅ **功能完整：支持表格对齐和格式保持**

项目现在可以正常编译，所有PDF转Excel转换功能都可以使用。