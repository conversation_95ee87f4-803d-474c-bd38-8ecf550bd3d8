package com.zte.crm.scrapy.util;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.*;
import java.util.List;
import java.util.ArrayList;
import java.util.regex.Pattern;
import java.util.regex.Matcher;

/**
 * 简化版PDF转Excel工具类
 * 基于文本处理的方式，避免复杂的PDF解析依赖
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-13
 */
public class SimplePdfToExcelConverter {
    
    /**
     * 将文本内容转换为Excel文件
     * 
     * @param textContent 文本内容
     * @param excelFilePath Excel输出文件路径
     * @throws IOException 文件操作异常
     */
    public static void convertTextToExcel(String textContent, String excelFilePath) throws IOException {
        try (Workbook workbook = new XSSFWorkbook();
             FileOutputStream fos = new FileOutputStream(excelFilePath)) {
            
            Sheet sheet = workbook.createSheet("Converted Data");
            
            String[] lines = textContent.split("\n");
            int rowIndex = 0;
            
            for (String line : lines) {
                line = line.trim();
                if (line.isEmpty()) continue;
                
                Row row = sheet.createRow(rowIndex++);
                
                // 尝试按空格或制表符分割
                String[] cells = splitLineIntoCells(line);
                
                for (int i = 0; i < cells.length; i++) {
                    Cell cell = row.createCell(i);
                    String cellText = cells[i].trim();
                    
                    if (isNumeric(cellText)) {
                        try {
                            double numValue = Double.parseDouble(cellText.replaceAll(",", ""));
                            cell.setCellValue(numValue);
                        } catch (NumberFormatException e) {
                            cell.setCellValue(cellText);
                        }
                    } else {
                        cell.setCellValue(cellText);
                    }
                }
            }
            
            // 自动调整列宽
            for (int i = 0; i < 20; i++) {
                try {
                    sheet.autoSizeColumn(i);
                } catch (Exception e) {
                    // 忽略错误
                    break;
                }
            }
            
            workbook.write(fos);
        }
    }
    
    /**
     * 将文本行分割为单元格
     * 
     * @param line 文本行
     * @return 单元格数组
     */
    private static String[] splitLineIntoCells(String line) {
        // 首先尝试按制表符分割
        if (line.contains("\t")) {
            return line.split("\t");
        }
        
        // 然后尝试按多个空格分割
        String[] parts = line.split("\\s{2,}");
        if (parts.length > 1) {
            return parts;
        }
        
        // 最后按单个空格分割，但保留引号内的内容
        List<String> cells = new ArrayList<>();
        Pattern pattern = Pattern.compile("\"([^\"]*)\"|\\S+");
        Matcher matcher = pattern.matcher(line);
        
        while (matcher.find()) {
            if (matcher.group(1) != null) {
                cells.add(matcher.group(1));
            } else {
                cells.add(matcher.group());
            }
        }
        
        return cells.toArray(new String[0]);
    }
    
    /**
     * 判断字符串是否为数字
     * 
     * @param str 字符串
     * @return 是否为数字
     */
    private static boolean isNumeric(String str) {
        if (str == null || str.isEmpty()) {
            return false;
        }
        
        // 移除千分位分隔符
        str = str.replaceAll(",", "");
        
        try {
            Double.parseDouble(str);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }
    
    /**
     * 从文件读取文本内容
     * 
     * @param filePath 文件路径
     * @return 文本内容
     * @throws IOException 文件操作异常
     */
    public static String readTextFromFile(String filePath) throws IOException {
        StringBuilder content = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(new FileInputStream(filePath), "UTF-8"))) {
            String line;
            while ((line = reader.readLine()) != null) {
                content.append(line).append("\n");
            }
        }
        return content.toString();
    }
    
    /**
     * 示例：处理表格格式的文本
     * 
     * @param textContent 文本内容
     * @param excelFilePath Excel输出文件路径
     * @throws IOException 文件操作异常
     */
    public static void convertTableTextToExcel(String textContent, String excelFilePath) throws IOException {
        try (Workbook workbook = new XSSFWorkbook();
             FileOutputStream fos = new FileOutputStream(excelFilePath)) {
            
            Sheet sheet = workbook.createSheet("Table Data");
            
            // 创建样式
            CellStyle headerStyle = workbook.createCellStyle();
            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            headerStyle.setFont(headerFont);
            headerStyle.setFillForegroundColor(IndexedColors.LIGHT_BLUE.getIndex());
            headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            
            CellStyle dataStyle = workbook.createCellStyle();
            dataStyle.setBorderBottom(BorderStyle.THIN);
            dataStyle.setBorderTop(BorderStyle.THIN);
            dataStyle.setBorderRight(BorderStyle.THIN);
            dataStyle.setBorderLeft(BorderStyle.THIN);
            
            String[] lines = textContent.split("\n");
            int rowIndex = 0;
            boolean isFirstRow = true;
            
            for (String line : lines) {
                line = line.trim();
                if (line.isEmpty()) continue;
                
                Row row = sheet.createRow(rowIndex++);
                String[] cells = splitLineIntoCells(line);
                
                for (int i = 0; i < cells.length; i++) {
                    Cell cell = row.createCell(i);
                    String cellText = cells[i].trim();
                    
                    // 设置样式
                    if (isFirstRow) {
                        cell.setCellStyle(headerStyle);
                        cell.setCellValue(cellText);
                    } else {
                        cell.setCellStyle(dataStyle);
                        
                        if (isNumeric(cellText)) {
                            try {
                                double numValue = Double.parseDouble(cellText.replaceAll(",", ""));
                                cell.setCellValue(numValue);
                            } catch (NumberFormatException e) {
                                cell.setCellValue(cellText);
                            }
                        } else {
                            cell.setCellValue(cellText);
                        }
                    }
                }
                
                isFirstRow = false;
            }
            
            // 自动调整列宽
            for (int i = 0; i < 20; i++) {
                try {
                    sheet.autoSizeColumn(i);
                } catch (Exception e) {
                    break;
                }
            }
            
            workbook.write(fos);
        }
    }
}
