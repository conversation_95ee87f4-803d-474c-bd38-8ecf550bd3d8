package com.alix.pdf2excel.service;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;

/**
 * PDF转Excel转换服务接口
 * 提供多种转换方式和配置选项
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-13
 */
public interface IPdfToExcelConverterService {
    
    /**
     * 转换结果类
     */
    class ConversionResult {
        private boolean success;
        private String message;
        private String outputFilePath;
        private long processingTimeMs;
        private int pagesProcessed;
        private long inputFileSizeBytes;
        private long outputFileSizeBytes;
        private String errorDetails;
        
        public ConversionResult() {}
        
        public ConversionResult(boolean success, String message) {
            this.success = success;
            this.message = message;
        }
        
        // Getters and setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        public String getOutputFilePath() { return outputFilePath; }
        public void setOutputFilePath(String outputFilePath) { this.outputFilePath = outputFilePath; }
        public long getProcessingTimeMs() { return processingTimeMs; }
        public void setProcessingTimeMs(long processingTimeMs) { this.processingTimeMs = processingTimeMs; }
        public int getPagesProcessed() { return pagesProcessed; }
        public void setPagesProcessed(int pagesProcessed) { this.pagesProcessed = pagesProcessed; }
        public long getInputFileSizeBytes() { return inputFileSizeBytes; }
        public void setInputFileSizeBytes(long inputFileSizeBytes) { this.inputFileSizeBytes = inputFileSizeBytes; }
        public long getOutputFileSizeBytes() { return outputFileSizeBytes; }
        public void setOutputFileSizeBytes(long outputFileSizeBytes) { this.outputFileSizeBytes = outputFileSizeBytes; }
        public String getErrorDetails() { return errorDetails; }
        public void setErrorDetails(String errorDetails) { this.errorDetails = errorDetails; }
        
        @Override
        public String toString() {
            if (success) {
                return String.format("转换成功！文件: %s, 处理页数: %d, 耗时: %d ms, 输入大小: %.2f KB, 输出大小: %.2f KB",
                    outputFilePath, pagesProcessed, processingTimeMs, 
                    inputFileSizeBytes / 1024.0, outputFileSizeBytes / 1024.0);
            } else {
                return String.format("转换失败: %s %s", message, 
                    errorDetails != null ? "(" + errorDetails + ")" : "");
            }
        }
    }
    
    /**
     * 转换配置类
     */
    class ConversionConfig {
        private boolean preserveTableAlignment = true;
        private boolean autoDetectNumbers = true;
        private boolean mergeCells = true;
        private boolean applyStyles = true;
        private boolean autoSizeColumns = true;
        private int maxColumnWidth = 15000;
        private boolean createSeparateSheets = true;
        private String sheetNamePrefix = "Page ";
        private boolean includeEmptyRows = false;
        private boolean detectHeaders = true;
        
        // Getters and setters
        public boolean isPreserveTableAlignment() { return preserveTableAlignment; }
        public void setPreserveTableAlignment(boolean preserveTableAlignment) { this.preserveTableAlignment = preserveTableAlignment; }
        public boolean isAutoDetectNumbers() { return autoDetectNumbers; }
        public void setAutoDetectNumbers(boolean autoDetectNumbers) { this.autoDetectNumbers = autoDetectNumbers; }
        public boolean isMergeCells() { return mergeCells; }
        public void setMergeCells(boolean mergeCells) { this.mergeCells = mergeCells; }
        public boolean isApplyStyles() { return applyStyles; }
        public void setApplyStyles(boolean applyStyles) { this.applyStyles = applyStyles; }
        public boolean isAutoSizeColumns() { return autoSizeColumns; }
        public void setAutoSizeColumns(boolean autoSizeColumns) { this.autoSizeColumns = autoSizeColumns; }
        public int getMaxColumnWidth() { return maxColumnWidth; }
        public void setMaxColumnWidth(int maxColumnWidth) { this.maxColumnWidth = maxColumnWidth; }
        public boolean isCreateSeparateSheets() { return createSeparateSheets; }
        public void setCreateSeparateSheets(boolean createSeparateSheets) { this.createSeparateSheets = createSeparateSheets; }
        public String getSheetNamePrefix() { return sheetNamePrefix; }
        public void setSheetNamePrefix(String sheetNamePrefix) { this.sheetNamePrefix = sheetNamePrefix; }
        public boolean isIncludeEmptyRows() { return includeEmptyRows; }
        public void setIncludeEmptyRows(boolean includeEmptyRows) { this.includeEmptyRows = includeEmptyRows; }
        public boolean isDetectHeaders() { return detectHeaders; }
        public void setDetectHeaders(boolean detectHeaders) { this.detectHeaders = detectHeaders; }
        
        /**
         * 创建默认配置
         */
        public static ConversionConfig getDefault() {
            return new ConversionConfig();
        }
        
        /**
         * 创建高质量转换配置
         */
        public static ConversionConfig getHighQuality() {
            ConversionConfig config = new ConversionConfig();
            config.setPreserveTableAlignment(true);
            config.setAutoDetectNumbers(true);
            config.setMergeCells(true);
            config.setApplyStyles(true);
            config.setAutoSizeColumns(true);
            config.setDetectHeaders(true);
            return config;
        }
        
        /**
         * 创建快速转换配置
         */
        public static ConversionConfig getFast() {
            ConversionConfig config = new ConversionConfig();
            config.setPreserveTableAlignment(false);
            config.setAutoDetectNumbers(false);
            config.setMergeCells(false);
            config.setApplyStyles(false);
            config.setAutoSizeColumns(false);
            config.setDetectHeaders(false);
            return config;
        }
    }
    
    /**
     * 将PDF文件转换为Excel文件（使用默认配置）
     * 
     * @param pdfFilePath PDF文件路径
     * @param excelFilePath Excel输出文件路径
     * @return 转换结果
     * @throws IOException 文件操作异常
     */
    ConversionResult convertPdfToExcel(String pdfFilePath, String excelFilePath) throws IOException;
    
    /**
     * 将PDF文件转换为Excel文件（使用指定配置）
     * 
     * @param pdfFilePath PDF文件路径
     * @param excelFilePath Excel输出文件路径
     * @param config 转换配置
     * @return 转换结果
     * @throws IOException 文件操作异常
     */
    ConversionResult convertPdfToExcel(String pdfFilePath, String excelFilePath, ConversionConfig config) throws IOException;
    
    /**
     * 将PDF文件转换为Excel文件（使用File对象）
     * 
     * @param pdfFile PDF文件
     * @param excelFile Excel输出文件
     * @return 转换结果
     * @throws IOException 文件操作异常
     */
    ConversionResult convertPdfToExcel(File pdfFile, File excelFile) throws IOException;
    
    /**
     * 将PDF文件转换为Excel文件（使用File对象和配置）
     * 
     * @param pdfFile PDF文件
     * @param excelFile Excel输出文件
     * @param config 转换配置
     * @return 转换结果
     * @throws IOException 文件操作异常
     */
    ConversionResult convertPdfToExcel(File pdfFile, File excelFile, ConversionConfig config) throws IOException;
    
    /**
     * 将PDF输入流转换为Excel文件
     * 
     * @param pdfInputStream PDF输入流
     * @param excelFilePath Excel输出文件路径
     * @return 转换结果
     * @throws IOException 文件操作异常
     */
    ConversionResult convertPdfToExcel(InputStream pdfInputStream, String excelFilePath) throws IOException;
    
    /**
     * 将PDF输入流转换为Excel文件（使用配置）
     * 
     * @param pdfInputStream PDF输入流
     * @param excelFilePath Excel输出文件路径
     * @param config 转换配置
     * @return 转换结果
     * @throws IOException 文件操作异常
     */
    ConversionResult convertPdfToExcel(InputStream pdfInputStream, String excelFilePath, ConversionConfig config) throws IOException;
    
    /**
     * 批量转换PDF文件到Excel
     * 
     * @param inputDirectory 输入目录
     * @param outputDirectory 输出目录
     * @return 批量转换结果列表
     * @throws IOException 文件操作异常
     */
    List<ConversionResult> batchConvertPdfToExcel(String inputDirectory, String outputDirectory) throws IOException;
    
    /**
     * 批量转换PDF文件到Excel（使用配置）
     * 
     * @param inputDirectory 输入目录
     * @param outputDirectory 输出目录
     * @param config 转换配置
     * @return 批量转换结果列表
     * @throws IOException 文件操作异常
     */
    List<ConversionResult> batchConvertPdfToExcel(String inputDirectory, String outputDirectory, ConversionConfig config) throws IOException;
    
    /**
     * 验证PDF文件是否有效
     * 
     * @param pdfFile PDF文件
     * @return 验证结果
     */
    boolean validatePdfFile(File pdfFile);
    
    /**
     * 验证PDF文件并获取详细信息
     * 
     * @param pdfFile PDF文件
     * @return PDF文件信息（页数、大小等）
     */
    PdfFileInfo analyzePdfFile(File pdfFile);
    
    /**
     * PDF文件信息类
     */
    class PdfFileInfo {
        private boolean valid;
        private int pageCount;
        private long fileSizeBytes;
        private String fileName;
        private boolean hasText;
        private boolean hasTables;
        private String errorMessage;
        
        public PdfFileInfo() {}
        
        public PdfFileInfo(boolean valid, String errorMessage) {
            this.valid = valid;
            this.errorMessage = errorMessage;
        }
        
        // Getters and setters
        public boolean isValid() { return valid; }
        public void setValid(boolean valid) { this.valid = valid; }
        public int getPageCount() { return pageCount; }
        public void setPageCount(int pageCount) { this.pageCount = pageCount; }
        public long getFileSizeBytes() { return fileSizeBytes; }
        public void setFileSizeBytes(long fileSizeBytes) { this.fileSizeBytes = fileSizeBytes; }
        public String getFileName() { return fileName; }
        public void setFileName(String fileName) { this.fileName = fileName; }
        public boolean isHasText() { return hasText; }
        public void setHasText(boolean hasText) { this.hasText = hasText; }
        public boolean isHasTables() { return hasTables; }
        public void setHasTables(boolean hasTables) { this.hasTables = hasTables; }
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
        
        @Override
        public String toString() {
            if (!valid) {
                return String.format("无效PDF文件: %s", errorMessage);
            }
            return String.format("PDF文件信息 - 名称: %s, 页数: %d, 大小: %.2f KB, 包含文本: %s, 包含表格: %s",
                fileName, pageCount, fileSizeBytes / 1024.0, hasText ? "是" : "否", hasTables ? "是" : "否");
        }
    }
    
    /**
     * 获取支持的输入格式
     * 
     * @return 支持的文件扩展名列表
     */
    List<String> getSupportedInputFormats();
    
    /**
     * 获取支持的输出格式
     * 
     * @return 支持的文件扩展名列表
     */
    List<String> getSupportedOutputFormats();
    
    /**
     * 获取服务版本信息
     * 
     * @return 版本信息
     */
    String getServiceVersion();
}
