<component name="libraryTable">
  <library name="aws-java-sdk-core-1.12.782">
    <CLASSES>
      <root url="jar://$PROJECT_DIR$/lib/aws-java-sdk-core-1.12.782.jar!/" />
      <root url="jar://$PROJECT_DIR$/lib/aws-java-sdk-kms-1.12.782.jar!/" />
      <root url="jar://$PROJECT_DIR$/lib/aws-java-sdk-s3-1.12.782.jar!/" />
      <root url="jar://$PROJECT_DIR$/lib/commons-codec-1.15.jar!/" />
      <root url="jar://$PROJECT_DIR$/lib/commons-logging-1.1.3.jar!/" />
      <root url="jar://$PROJECT_DIR$/lib/httpclient-4.5.13.jar!/" />
      <root url="jar://$PROJECT_DIR$/lib/httpcore-4.4.13.jar!/" />
      <root url="jar://$PROJECT_DIR$/lib/jmespath-java-1.12.782.jar!/" />
      <root url="jar://$PROJECT_DIR$/lib/joda-time-2.12.7.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES />
  </library>
</component>