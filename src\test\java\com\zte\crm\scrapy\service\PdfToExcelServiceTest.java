package com.zte.crm.scrapy.service;

import com.zte.crm.scrapy.service.impl.PdfToExcelServiceImpl;
import org.junit.Test;
import org.junit.Before;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

import static org.junit.Assert.*;

/**
 * PDF转Excel服务测试类
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-13
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class PdfToExcelServiceTest {
    
    private IPdfToExcelService pdfToExcelService;
    private String testPdfPath;
    private String testExcelPath;
    
    @Before
    public void setUp() {
        pdfToExcelService = new PdfToExcelServiceImpl();
        
        // 使用项目中的测试PDF文件
        testPdfPath = "doc/Test_HK4934_page10.pdf";
        testExcelPath = "target/test_output.xlsx";
    }
    
    @Test
    public void testConvertPdfToExcel() {
        try {
            File pdfFile = new File(testPdfPath);
            
            // 检查测试文件是否存在
            if (!pdfFile.exists()) {
                System.out.println("测试PDF文件不存在，跳过测试: " + testPdfPath);
                return;
            }
            
            // 执行转换
            String result = pdfToExcelService.convertPdfToExcel(testPdfPath, testExcelPath);
            
            // 验证结果
            assertNotNull("转换结果不应为空", result);
            assertTrue("结果应包含成功信息", result.contains("转换成功"));
            
            // 验证输出文件
            File excelFile = new File(testExcelPath);
            assertTrue("Excel文件应该被创建", excelFile.exists());
            assertTrue("Excel文件大小应大于0", excelFile.length() > 0);
            
            System.out.println("转换结果: " + result);
            System.out.println("Excel文件路径: " + excelFile.getAbsolutePath());
            
        } catch (IOException e) {
            fail("转换过程中发生异常: " + e.getMessage());
        }
    }
    
    @Test
    public void testValidatePdfFile() {
        File pdfFile = new File(testPdfPath);
        
        if (pdfFile.exists()) {
            boolean isValid = pdfToExcelService.validatePdfFile(pdfFile);
            assertTrue("PDF文件应该是有效的", isValid);
        }
        
        // 测试无效文件
        File invalidFile = new File("nonexistent.pdf");
        boolean isInvalid = pdfToExcelService.validatePdfFile(invalidFile);
        assertFalse("不存在的文件应该是无效的", isInvalid);
    }
    
    @Test
    public void testGetPdfPageCount() {
        try {
            File pdfFile = new File(testPdfPath);
            
            if (!pdfFile.exists()) {
                System.out.println("测试PDF文件不存在，跳过测试: " + testPdfPath);
                return;
            }
            
            int pageCount = pdfToExcelService.getPdfPageCount(pdfFile);
            assertTrue("页数应该大于0", pageCount > 0);
            
            System.out.println("PDF页数: " + pageCount);
            
        } catch (IOException e) {
            fail("获取页数时发生异常: " + e.getMessage());
        }
    }
    
    @Test
    public void testConvertWithReferenceFiles() {
        try {
            // 使用参照文件进行测试
            String referencePdf = "doc/Test_HK4934_page10.pdf";
            String outputExcel = "target/test_reference_output.xlsx";
            
            File pdfFile = new File(referencePdf);
            if (!pdfFile.exists()) {
                System.out.println("参照PDF文件不存在，跳过测试: " + referencePdf);
                return;
            }
            
            // 执行转换
            String result = pdfToExcelService.convertPdfToExcel(referencePdf, outputExcel);
            
            // 验证结果
            assertNotNull("转换结果不应为空", result);
            
            File excelFile = new File(outputExcel);
            assertTrue("Excel文件应该被创建", excelFile.exists());
            
            // 比较参照Excel文件
            File referenceExcel = new File("doc/Test_HK4934_page10.xlsx");
            if (referenceExcel.exists()) {
                System.out.println("参照Excel文件大小: " + referenceExcel.length() + " bytes");
                System.out.println("生成Excel文件大小: " + excelFile.length() + " bytes");
                
                // 这里可以添加更详细的内容比较逻辑
                assertTrue("生成的Excel文件应该有合理的大小", excelFile.length() > 1000);
            }
            
            System.out.println("参照文件转换结果: " + result);
            
        } catch (IOException e) {
            fail("参照文件转换过程中发生异常: " + e.getMessage());
        }
    }
}
