package com.zte.crm.scrapy;

import com.zte.crm.scrapy.util.SimplePdfToExcelConverter;

import java.io.File;

/**
 * 简单的Excel生成测试
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-13
 */
public class SimpleExcelTest {
    
    public static void main(String[] args) {
        System.out.println("=== 简单Excel生成测试 ===");
        
        // 创建示例数据
        String sampleData = createSampleTableData();
        
        try {
            String outputPath = "doc/sample_output.xlsx";
            
            System.out.println("生成示例Excel文件...");
            System.out.println("输出路径: " + outputPath);
            
            // 转换为Excel
            SimplePdfToExcelConverter.convertTableTextToExcel(sampleData, outputPath);
            
            // 验证文件
            File outputFile = new File(outputPath);
            if (outputFile.exists()) {
                System.out.println("Excel文件生成成功！");
                System.out.println("文件大小: " + String.format("%.2f KB", outputFile.length() / 1024.0));
                System.out.println("文件路径: " + outputFile.getAbsolutePath());
            } else {
                System.err.println("Excel文件生成失败！");
            }
            
        } catch (Exception e) {
            System.err.println("生成Excel失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 创建示例表格数据
     */
    private static String createSampleTableData() {
        StringBuilder sb = new StringBuilder();
        
        // 表头
        sb.append("项目名称\t项目编号\t金额\t状态\t日期\n");
        
        // 数据行
        sb.append("测试项目A\tPRJ001\t1000000\t进行中\t2025-01-13\n");
        sb.append("测试项目B\tPRJ002\t2500000\t已完成\t2025-01-12\n");
        sb.append("测试项目C\tPRJ003\t800000\t待开始\t2025-01-14\n");
        sb.append("测试项目D\tPRJ004\t1500000\t进行中\t2025-01-11\n");
        sb.append("测试项目E\tPRJ005\t3200000\t已完成\t2025-01-10\n");
        
        // 添加一些复杂的数据
        sb.append("\n");
        sb.append("财务数据统计\n");
        sb.append("收入类型\t第一季度\t第二季度\t第三季度\t第四季度\n");
        sb.append("产品销售\t1,200,000\t1,350,000\t1,180,000\t1,420,000\n");
        sb.append("服务收入\t800,000\t920,000\t850,000\t980,000\n");
        sb.append("其他收入\t150,000\t180,000\t160,000\t200,000\n");
        
        return sb.toString();
    }
}
