package com.alix.pdf2excel.test;

import com.alix.pdf2excel.service.IPdfToExcelConverterService;
import com.alix.pdf2excel.service.impl.PdfToExcelConverterServiceImpl;
import com.alix.pdf2excel.util.EnhancedPdfToExcelConverter;
import org.junit.Test;
import org.junit.Before;
import org.junit.After;
import static org.junit.Assert.*;

import java.io.File;
import java.io.IOException;
import java.util.List;

/**
 * PDF转Excel转换器测试类
 * 验证转换功能和效果
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-13
 */
public class PdfToExcelConverterTest {
    
    private IPdfToExcelConverterService converterService;
    private File testInputDir;
    private File testOutputDir;
    private File samplePdfFile;
    
    @Before
    public void setUp() {
        converterService = new PdfToExcelConverterServiceImpl();
        
        // 设置测试目录
        testInputDir = new File("test_input");
        testOutputDir = new File("test_output");
        
        // 创建测试目录
        if (!testInputDir.exists()) {
            testInputDir.mkdirs();
        }
        if (!testOutputDir.exists()) {
            testOutputDir.mkdirs();
        }
        
        // 设置样本PDF文件（使用项目中已有的测试文件）
        samplePdfFile = new File("doc/Test_HK4934_page10.pdf");
    }
    
    @After
    public void tearDown() {
        // 清理测试输出文件（可选）
        // cleanupTestFiles();
    }
    
    /**
     * 测试基本的PDF转Excel功能
     */
    @Test
    public void testBasicPdfToExcelConversion() {
        if (!samplePdfFile.exists()) {
            System.out.println("跳过测试：样本PDF文件不存在 - " + samplePdfFile.getAbsolutePath());
            return;
        }
        
        try {
            File outputFile = new File(testOutputDir, "test_basic_conversion.xlsx");
            
            IPdfToExcelConverterService.ConversionResult result = 
                converterService.convertPdfToExcel(samplePdfFile, outputFile);
            
            assertTrue("转换应该成功", result.isSuccess());
            assertTrue("输出文件应该存在", outputFile.exists());
            assertTrue("输出文件应该有内容", outputFile.length() > 0);
            assertTrue("应该处理至少1页", result.getPagesProcessed() > 0);
            assertTrue("处理时间应该大于0", result.getProcessingTimeMs() > 0);
            
            System.out.println("基本转换测试结果：");
            System.out.println(result.toString());
            
        } catch (IOException e) {
            fail("转换过程中不应该抛出异常: " + e.getMessage());
        }
    }
    
    /**
     * 测试高质量转换配置
     */
    @Test
    public void testHighQualityConversion() {
        if (!samplePdfFile.exists()) {
            System.out.println("跳过测试：样本PDF文件不存在");
            return;
        }
        
        try {
            File outputFile = new File(testOutputDir, "test_high_quality.xlsx");
            IPdfToExcelConverterService.ConversionConfig config = 
                IPdfToExcelConverterService.ConversionConfig.getHighQuality();
            
            IPdfToExcelConverterService.ConversionResult result = 
                converterService.convertPdfToExcel(samplePdfFile, outputFile, config);
            
            assertTrue("高质量转换应该成功", result.isSuccess());
            assertTrue("输出文件应该存在", outputFile.exists());
            
            System.out.println("高质量转换测试结果：");
            System.out.println(result.toString());
            
        } catch (IOException e) {
            fail("高质量转换不应该抛出异常: " + e.getMessage());
        }
    }
    
    /**
     * 测试快速转换配置
     */
    @Test
    public void testFastConversion() {
        if (!samplePdfFile.exists()) {
            System.out.println("跳过测试：样本PDF文件不存在");
            return;
        }
        
        try {
            File outputFile = new File(testOutputDir, "test_fast_conversion.xlsx");
            IPdfToExcelConverterService.ConversionConfig config = 
                IPdfToExcelConverterService.ConversionConfig.getFast();
            
            long startTime = System.currentTimeMillis();
            IPdfToExcelConverterService.ConversionResult result = 
                converterService.convertPdfToExcel(samplePdfFile, outputFile, config);
            long endTime = System.currentTimeMillis();
            
            assertTrue("快速转换应该成功", result.isSuccess());
            assertTrue("输出文件应该存在", outputFile.exists());
            
            System.out.println("快速转换测试结果：");
            System.out.println(result.toString());
            System.out.println("实际耗时: " + (endTime - startTime) + " ms");
            
        } catch (IOException e) {
            fail("快速转换不应该抛出异常: " + e.getMessage());
        }
    }
    
    /**
     * 测试PDF文件验证功能
     */
    @Test
    public void testPdfFileValidation() {
        // 测试有效文件
        if (samplePdfFile.exists()) {
            assertTrue("样本PDF文件应该有效", converterService.validatePdfFile(samplePdfFile));
        }
        
        // 测试无效文件
        File nonExistentFile = new File("non_existent_file.pdf");
        assertFalse("不存在的文件应该无效", converterService.validatePdfFile(nonExistentFile));
        
        // 测试非PDF文件
        File textFile = new File("test.txt");
        try {
            if (!textFile.exists()) {
                textFile.createNewFile();
            }
            assertFalse("文本文件应该无效", converterService.validatePdfFile(textFile));
        } catch (IOException e) {
            System.out.println("创建测试文件失败，跳过此测试");
        } finally {
            if (textFile.exists()) {
                textFile.delete();
            }
        }
    }
    
    /**
     * 测试PDF文件分析功能
     */
    @Test
    public void testPdfFileAnalysis() {
        if (!samplePdfFile.exists()) {
            System.out.println("跳过测试：样本PDF文件不存在");
            return;
        }
        
        IPdfToExcelConverterService.PdfFileInfo info = converterService.analyzePdfFile(samplePdfFile);
        
        assertTrue("PDF文件应该有效", info.isValid());
        assertTrue("页数应该大于0", info.getPageCount() > 0);
        assertTrue("文件大小应该大于0", info.getFileSizeBytes() > 0);
        assertNotNull("文件名不应该为空", info.getFileName());
        
        System.out.println("PDF文件分析结果：");
        System.out.println(info.toString());
    }
    
    /**
     * 测试批量转换功能
     */
    @Test
    public void testBatchConversion() {
        if (!samplePdfFile.exists()) {
            System.out.println("跳过测试：样本PDF文件不存在");
            return;
        }
        
        try {
            // 复制样本文件到测试输入目录
            File testPdf1 = new File(testInputDir, "test1.pdf");
            File testPdf2 = new File(testInputDir, "test2.pdf");
            
            // 简单复制（实际应用中可能需要更完整的文件复制）
            if (samplePdfFile.exists()) {
                try {
                    java.nio.file.Files.copy(samplePdfFile.toPath(), testPdf1.toPath(), 
                        java.nio.file.StandardCopyOption.REPLACE_EXISTING);
                    java.nio.file.Files.copy(samplePdfFile.toPath(), testPdf2.toPath(), 
                        java.nio.file.StandardCopyOption.REPLACE_EXISTING);
                } catch (IOException e) {
                    System.out.println("复制测试文件失败，跳过批量转换测试");
                    return;
                }
            }
            
            List<IPdfToExcelConverterService.ConversionResult> results = 
                converterService.batchConvertPdfToExcel(
                    testInputDir.getAbsolutePath(), 
                    testOutputDir.getAbsolutePath());
            
            assertNotNull("批量转换结果不应该为空", results);
            assertTrue("应该有转换结果", results.size() > 0);
            
            int successCount = 0;
            for (IPdfToExcelConverterService.ConversionResult result : results) {
                if (result.isSuccess()) {
                    successCount++;
                }
                System.out.println("批量转换结果: " + result.toString());
            }
            
            assertTrue("应该有成功的转换", successCount > 0);
            
            // 获取统计信息
            if (converterService instanceof PdfToExcelConverterServiceImpl) {
                PdfToExcelConverterServiceImpl impl = (PdfToExcelConverterServiceImpl) converterService;
                String statistics = impl.getConversionStatistics(results);
                System.out.println("批量转换统计：");
                System.out.println(statistics);
            }
            
        } catch (IOException e) {
            fail("批量转换测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试服务信息功能
     */
    @Test
    public void testServiceInfo() {
        List<String> inputFormats = converterService.getSupportedInputFormats();
        List<String> outputFormats = converterService.getSupportedOutputFormats();
        String version = converterService.getServiceVersion();
        
        assertNotNull("支持的输入格式不应该为空", inputFormats);
        assertNotNull("支持的输出格式不应该为空", outputFormats);
        assertNotNull("版本信息不应该为空", version);
        
        assertTrue("应该支持PDF输入", inputFormats.contains("pdf"));
        assertTrue("应该支持Excel输出", outputFormats.contains("xlsx") || outputFormats.contains("xls"));
        
        System.out.println("服务信息：");
        System.out.println("版本: " + version);
        System.out.println("支持的输入格式: " + inputFormats);
        System.out.println("支持的输出格式: " + outputFormats);
    }
    
    /**
     * 测试错误处理
     */
    @Test
    public void testErrorHandling() {
        try {
            // 测试不存在的输入文件
            File nonExistentPdf = new File("non_existent.pdf");
            File outputFile = new File(testOutputDir, "error_test.xlsx");
            
            IPdfToExcelConverterService.ConversionResult result = 
                converterService.convertPdfToExcel(nonExistentPdf, outputFile);
            
            assertFalse("转换应该失败", result.isSuccess());
            assertNotNull("应该有错误信息", result.getMessage());
            
            System.out.println("错误处理测试结果：");
            System.out.println(result.toString());
            
        } catch (IOException e) {
            fail("错误处理测试不应该抛出异常: " + e.getMessage());
        }
    }
    
    /**
     * 比较不同配置的转换结果
     */
    @Test
    public void testCompareConfigurations() {
        if (!samplePdfFile.exists()) {
            System.out.println("跳过测试：样本PDF文件不存在");
            return;
        }
        
        try {
            // 默认配置
            File defaultOutput = new File(testOutputDir, "comparison_default.xlsx");
            IPdfToExcelConverterService.ConversionResult defaultResult = 
                converterService.convertPdfToExcel(samplePdfFile, defaultOutput);
            
            // 高质量配置
            File highQualityOutput = new File(testOutputDir, "comparison_high_quality.xlsx");
            IPdfToExcelConverterService.ConversionResult highQualityResult = 
                converterService.convertPdfToExcel(samplePdfFile, highQualityOutput, 
                    IPdfToExcelConverterService.ConversionConfig.getHighQuality());
            
            // 快速配置
            File fastOutput = new File(testOutputDir, "comparison_fast.xlsx");
            IPdfToExcelConverterService.ConversionResult fastResult = 
                converterService.convertPdfToExcel(samplePdfFile, fastOutput, 
                    IPdfToExcelConverterService.ConversionConfig.getFast());
            
            System.out.println("配置比较结果：");
            System.out.println("默认配置: " + defaultResult.toString());
            System.out.println("高质量配置: " + highQualityResult.toString());
            System.out.println("快速配置: " + fastResult.toString());
            
            // 比较处理时间
            if (defaultResult.isSuccess() && highQualityResult.isSuccess() && fastResult.isSuccess()) {
                System.out.println("处理时间比较：");
                System.out.println("默认: " + defaultResult.getProcessingTimeMs() + " ms");
                System.out.println("高质量: " + highQualityResult.getProcessingTimeMs() + " ms");
                System.out.println("快速: " + fastResult.getProcessingTimeMs() + " ms");
            }
            
        } catch (IOException e) {
            fail("配置比较测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 清理测试文件
     */
    private void cleanupTestFiles() {
        deleteDirectory(testOutputDir);
        deleteDirectory(testInputDir);
    }
    
    /**
     * 递归删除目录
     */
    private void deleteDirectory(File directory) {
        if (directory != null && directory.exists()) {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isDirectory()) {
                        deleteDirectory(file);
                    } else {
                        file.delete();
                    }
                }
            }
            directory.delete();
        }
    }
    
    /**
     * 主方法，用于运行所有测试
     */
    public static void main(String[] args) {
        System.out.println("开始PDF转Excel转换器测试...");
        
        PdfToExcelConverterTest test = new PdfToExcelConverterTest();
        
        try {
            test.setUp();
            
            System.out.println("\n=== 基本转换测试 ===");
            test.testBasicPdfToExcelConversion();
            
            System.out.println("\n=== 高质量转换测试 ===");
            test.testHighQualityConversion();
            
            System.out.println("\n=== 快速转换测试 ===");
            test.testFastConversion();
            
            System.out.println("\n=== PDF验证测试 ===");
            test.testPdfFileValidation();
            
            System.out.println("\n=== PDF分析测试 ===");
            test.testPdfFileAnalysis();
            
            System.out.println("\n=== 批量转换测试 ===");
            test.testBatchConversion();
            
            System.out.println("\n=== 服务信息测试 ===");
            test.testServiceInfo();
            
            System.out.println("\n=== 错误处理测试 ===");
            test.testErrorHandling();
            
            System.out.println("\n=== 配置比较测试 ===");
            test.testCompareConfigurations();
            
            test.tearDown();
            
            System.out.println("\n所有测试完成！");
            
        } catch (Exception e) {
            System.err.println("测试过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
