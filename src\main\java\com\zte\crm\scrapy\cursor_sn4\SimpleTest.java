package com.zte.crm.scrapy.cursor_sn4;

import java.io.File;

/**
 * 简单测试程序
 */
public class SimpleTest {
    
    public static void main(String[] args) {
        System.out.println("=== PDF to Excel Simple Test ===");
        
        try {
            // 测试输入输出路径
            String inputPath = "doc/Test_HK4934_page10.pdf";
            String outputPath = "src/main/java/com/zte/crm/scrapy/cursor_sn4/test_output.xlsx";
            
            File inputFile = new File(inputPath);
            File outputFile = new File(outputPath);
            
            System.out.println("Input file: " + inputFile.getAbsolutePath());
            System.out.println("Input file exists: " + inputFile.exists());
            
            if (inputFile.exists()) {
                System.out.println("Starting conversion...");
                
                // 使用工具类直接转换
                long startTime = System.currentTimeMillis();
                EnhancedPdfToExcelConverter.convertPdfToExcel(inputFile, outputFile);
                long endTime = System.currentTimeMillis();
                
                System.out.println("Conversion completed!");
                System.out.println("Time taken: " + (endTime - startTime) + " ms");
                System.out.println("Output file: " + outputFile.getAbsolutePath());
                System.out.println("Output file exists: " + outputFile.exists());
                System.out.println("Output file size: " + String.format("%.2f KB", outputFile.length() / 1024.0));
                
                // 使用服务接口
                System.out.println("\n--- Testing Service Interface ---");
                IPdfToExcelConverterService service = new PdfToExcelConverterServiceImpl();
                File outputFile2 = new File("src/main/java/com/zte/crm/scrapy/cursor_sn4/test_output_service.xlsx");
                
                IPdfToExcelConverterService.ConversionResult result = 
                    service.convertPdfToExcel(inputFile, outputFile2);
                
                System.out.println("Service result: " + result.toString());
                
            } else {
                System.out.println("Input file not found: " + inputPath);
                System.out.println("Please make sure the test PDF file exists.");
            }
            
        } catch (Exception e) {
            System.err.println("Error during conversion: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("=== Test Completed ===");
    }
}
